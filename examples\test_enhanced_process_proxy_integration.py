#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EnhancedProcess ProcessProxy 集成功能专项测试
==========================================

该测试文件专门测试 EnhancedProcess 类的 ProcessProxy 集成功能，
验证集成的易用性、稳定性和正确性。

测试覆盖：
1. 基本集成功能测试
2. 便捷工厂方法测试
3. 错误处理和异常恢复测试
4. 性能和稳定性测试
5. 生命周期管理测试

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import time
import multiprocessing
import traceback
import asyncio
from typing import Dict, Any

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

# 导入被测试的模块
from global_tools.utils.enhanced_process import (
    EnhancedProcess, 
    create_proxy_process,
    create_simple_proxy_worker,
    ProxyConfiguration
)


class TestService:
    """
    测试服务类 - 用于验证跨进程服务调用
    
    该类提供多种类型的方法和属性，用于全面测试代理功能：
    - 计数器功能（验证状态修改）
    - 数据存储（验证复杂数据结构）
    - 异步操作（验证异步方法调用）
    """
    
    def __init__(self, name: str = "TestService"):
        """初始化测试服务"""
        self.name = name
        self.counter = 0
        self.data_store = {}
        self.call_history = []
        self.created_at = time.time()
        
        print(f"[主进程 {os.getpid()}] TestService '{name}' 已创建")
    
    def increment(self, step: int = 1) -> int:
        """增加计数器"""
        old_value = self.counter
        self.counter += step
        
        call_info = f"increment({step}): {old_value} -> {self.counter}"
        self.call_history.append(call_info)
        
        print(f"[进程 {os.getpid()}] {call_info}")
        return self.counter
    
    def store_data(self, key: str, value: Any) -> bool:
        """存储数据"""
        self.data_store[key] = value
        call_info = f"store_data('{key}', {value})"
        self.call_history.append(call_info)
        
        print(f"[进程 {os.getpid()}] {call_info}")
        return True
    
    def get_data(self, key: str) -> Any:
        """获取数据"""
        value = self.data_store.get(key)
        call_info = f"get_data('{key}') -> {value}"
        self.call_history.append(call_info)
        
        print(f"[进程 {os.getpid()}] {call_info}")
        return value
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        status = {
            'name': self.name,
            'counter': self.counter,
            'data_count': len(self.data_store),
            'call_count': len(self.call_history),
            'uptime': time.time() - self.created_at
        }
        
        print(f"[进程 {os.getpid()}] get_status() -> {status}")
        return status
    
    def simulate_error(self, error_type: str = "runtime"):
        """模拟错误，用于测试错误处理"""
        if error_type == "runtime":
            raise RuntimeError("模拟的运行时错误")
        elif error_type == "value":
            raise ValueError("模拟的值错误")
        else:
            raise Exception(f"模拟的{error_type}错误")


def basic_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    """
    基本工作函数 - 测试基础代理功能
    """
    try:
        print(f"[子进程 {os.getpid()}] 基本代理功能测试开始")
        
        # 获取服务代理
        service = proxy_accessor.get_proxy('test_service')
        
        # 基本操作测试
        service.increment(5)
        service.store_data('test_key', 'test_value')
        value = service.get_data('test_key')
        status = service.get_status()
        
        print(f"[子进程 {os.getpid()}] 基本操作完成，状态: {status}")
        return True
        
    except Exception as e:
        print(f"[子进程 {os.getpid()}] 基本工作函数异常: {e}")
        traceback.print_exc()
        return False


def attribute_access_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    """
    属性访问工作函数 - 测试属性访问方式
    """
    try:
        print(f"[子进程 {os.getpid()}] 属性访问测试开始")
        
        # 通过属性访问获取服务代理
        service = proxy_accessor.test_service
        
        # 测试属性访问
        service.increment(3)
        service.store_data('attr_key', 'attr_value')
        
        # 列出可用对象
        available = proxy_accessor.list_available_objects()
        print(f"[子进程 {os.getpid()}] 可用对象: {available}")
        
        status = service.get_status()
        print(f"[子进程 {os.getpid()}] 属性访问完成，状态: {status}")
        return True
        
    except Exception as e:
        print(f"[子进程 {os.getpid()}] 属性访问工作函数异常: {e}")
        traceback.print_exc()
        return False


def error_handling_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    """
    错误处理工作函数 - 测试错误处理机制
    """
    try:
        print(f"[子进程 {os.getpid()}] 错误处理测试开始")
        
        service = proxy_accessor.get_proxy('test_service')
        
        # 正常操作
        service.increment(1)
        
        # 测试错误处理
        try:
            service.simulate_error("runtime")
        except RuntimeError as e:
            print(f"[子进程 {os.getpid()}] 成功捕获运行时错误: {e}")
        
        try:
            service.simulate_error("value")
        except ValueError as e:
            print(f"[子进程 {os.getpid()}] 成功捕获值错误: {e}")
        
        # 验证服务仍然可用
        status = service.get_status()
        print(f"[子进程 {os.getpid()}] 错误处理完成，服务状态: {status}")
        return True
        
    except Exception as e:
        print(f"[子进程 {os.getpid()}] 错误处理工作函数异常: {e}")
        traceback.print_exc()
        return False


def test_basic_integration():
    """测试基本集成功能"""
    print("\n" + "=" * 60)
    print("测试1: 基本集成功能")
    print("=" * 60)
    
    try:
        # 创建测试服务
        test_service = TestService("BasicTest")
        
        # 使用 create_with_proxy 创建进程
        process = EnhancedProcess.create_with_proxy(
            target=basic_worker,
            proxy_objects={'test_service': test_service}
        )
        
        # 启动并等待完成
        process.start()
        success = process.wait_for_completion(timeout=10)
        
        if success:
            final_status = test_service.get_status()
            print(f"✅ 基本集成测试成功，最终状态: {final_status}")
            return True
        else:
            print("❌ 基本集成测试超时")
            return False
            
    except Exception as e:
        print(f"❌ 基本集成测试异常: {e}")
        traceback.print_exc()
        return False


def test_factory_function():
    """测试便捷工厂函数"""
    print("\n" + "=" * 60)
    print("测试2: 便捷工厂函数")
    print("=" * 60)
    
    try:
        # 创建测试服务
        test_service = TestService("FactoryTest")
        
        # 使用便捷工厂函数创建进程
        process = create_proxy_process(
            target=attribute_access_worker,
            proxy_objects={'test_service': test_service}
        )
        
        # 启动并等待完成
        process.start()
        success = process.wait_for_completion(timeout=10)
        
        if success:
            final_status = test_service.get_status()
            print(f"✅ 工厂函数测试成功，最终状态: {final_status}")
            return True
        else:
            print("❌ 工厂函数测试超时")
            return False
            
    except Exception as e:
        print(f"❌ 工厂函数测试异常: {e}")
        traceback.print_exc()
        return False


def test_error_handling():
    """测试错误处理机制"""
    print("\n" + "=" * 60)
    print("测试3: 错误处理机制")
    print("=" * 60)
    
    try:
        # 创建测试服务
        test_service = TestService("ErrorTest")
        
        # 创建进程
        process = EnhancedProcess.create_with_proxy(
            target=error_handling_worker,
            proxy_objects={'test_service': test_service}
        )
        
        # 启动并等待完成
        process.start()
        success = process.wait_for_completion(timeout=10)
        
        if success:
            final_status = test_service.get_status()
            print(f"✅ 错误处理测试成功，最终状态: {final_status}")
            return True
        else:
            print("❌ 错误处理测试超时")
            return False
            
    except Exception as e:
        print(f"❌ 错误处理测试异常: {e}")
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=" * 80)
    print("EnhancedProcess ProcessProxy 集成功能专项测试")
    print("=" * 80)
    
    # 运行所有测试
    test_results = []
    
    test_results.append(test_basic_integration())
    test_results.append(test_factory_function())
    test_results.append(test_error_handling())
    
    # 汇总结果
    passed = sum(test_results)
    total = len(test_results)
    
    print("\n" + "=" * 80)
    print("测试结果汇总")
    print("=" * 80)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    
    if passed == total:
        print("\n🎉 所有EnhancedProcess集成测试通过！")
        return True
    else:
        print(f"\n❌ {total - passed} 个测试失败！")
        return False


if __name__ == "__main__":
    # 设置多进程启动方法
    if hasattr(multiprocessing, 'set_start_method'):
        try:
            multiprocessing.set_start_method('spawn', force=True)
        except RuntimeError:
            pass  # 已经设置过了
    
    success = main()
    sys.exit(0 if success else 1)
