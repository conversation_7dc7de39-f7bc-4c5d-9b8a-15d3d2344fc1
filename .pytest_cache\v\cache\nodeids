["global_tools/utils/event.py::test_emit_return_values", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_backward_compatibility", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_data_queue_functionality", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_enhanced_process_creation", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_enhanced_process_start_and_join", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_error_handling", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_multiple_processes", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_parameter_passing", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_process_logger", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_process_termination", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_proxy_disabled_by_default", "test/test_enhanced_process/test_basic_functionality.py::TestBasicFunctionality::test_shared_data_access", "test_proxy_manager.py::TestCrossProcessCommunication::test_child_process_calls_main_process", "tests/test_environment_detection.py::TestEnvironmentDetector::test_best_environment_selection", "tests/test_environment_detection.py::TestEnvironmentDetector::test_cache_expiration", "tests/test_environment_detection.py::TestEnvironmentDetector::test_caching_mechanism", "tests/test_environment_detection.py::TestEnvironmentDetector::test_config_file_detection", "tests/test_environment_detection.py::TestEnvironmentDetector::test_consistency_validation", "tests/test_environment_detection.py::TestEnvironmentDetector::test_detection_details", "tests/test_environment_detection.py::TestEnvironmentDetector::test_environment_variable_detection", "tests/test_environment_detection.py::TestEnvironmentDetector::test_error_handling", "tests/test_environment_detection.py::TestEnvironmentDetector::test_force_redetection", "tests/test_environment_detection.py::TestEnvironmentDetector::test_multi_layer_detection", "tests/test_environment_detection.py::TestEnvironmentDetector::test_network_environment_detection", "tests/test_environment_detection.py::TestEnvironmentDetector::test_process_arguments_detection", "tests/test_environment_detection.py::TestEnvironmentDetector::test_runtime_features_detection", "tests/test_environment_detection.py::TestEnvironmentDetector::test_supported_environments", "tests/test_environment_detection.py::TestEnvironmentDetector::test_system_properties_detection", "tests/test_environment_detection.py::TestEnvironmentManager::test_environment_setting", "tests/test_environment_detection.py::TestEnvironmentManager::test_singleton_pattern"]