# -*- coding: utf-8 -*-
"""
增强进程模块入口。
导出 EnhancedProcess, EnhancedProcessWithSharedData 和 ResultContainer。
"""
from global_tools.utils import Logger, ClassInstanceManager, Colors, LogLevel

ClassInstanceManager.create_instance(Logger, "EnhancedProcessLogger")
logger: Logger = ClassInstanceManager.get_instance("EnhancedProcessLogger")
logger.set_instance_level(LogLevel.OFF)

# 为ProcessProxy模块创建日志记录器
ClassInstanceManager.create_instance(Logger, "ProcessProxyLogger")
proxy_logger: Logger = ClassInstanceManager.get_instance("ProcessProxyLogger")
proxy_logger.set_instance_level(LogLevel.OFF)

# 为ProxyAccessor模块创建日志记录器
ClassInstanceManager.create_instance(Logger, "ProxyAccessorLogger")
accessor_logger: Logger = ClassInstanceManager.get_instance("ProxyAccessorLogger")
accessor_logger.set_instance_level(LogLevel.OFF)

from .process import EnhancedProcess, ProcessLogger
from .shared_data import SharedDataManager
from .proxy_manager import (
    ProcessProxyManager,
    ProcessProxy,
    ProxyFactory,
    ProxyConfiguration,
    ErrorHandler,
    PerformanceMonitor,
    StateManager,
    CallDispatcher,
    CommandProcessor,
    create_proxy_manager,
    create_proxy
)
from .proxy_accessor import ProxyAccessor
from .proxy_config import (
    ProcessProxyConfig,
    create_default_config,
    create_debug_config,
    create_performance_config
)
from .decorators import (
    with_proxy_objects,
    process_proxy_enabled,
    with_proxy_config,
    ProcessProxyContext,
    extract_proxy_config
)

__all__ = [
    # 原有的增强进程类
    "EnhancedProcess",
    "ProcessLogger",
    "SharedDataManager",

    # 新增的进程代理类
    "ProcessProxyManager",
    "ProcessProxy",
    "ProxyFactory",
    "ProxyConfiguration",
    "ErrorHandler",
    "PerformanceMonitor",
    "StateManager",
    "CallDispatcher",
    "CommandProcessor",
    "ProxyAccessor",
    "ProcessProxyConfig",

    # 装饰器和上下文管理器
    "with_proxy_objects",
    "process_proxy_enabled",
    "with_proxy_config",
    "ProcessProxyContext",
    "extract_proxy_config",

    # 便捷函数
    "create_proxy_manager",
    "create_proxy",
    "create_default_config",
    "create_debug_config",
    "create_performance_config",
]
