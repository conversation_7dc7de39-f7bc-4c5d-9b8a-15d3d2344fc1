# -*- coding: utf-8 -*-
"""
增强进程模块 - 高级多进程控制与跨进程代理系统
==============================================

本模块提供了完整的多进程解决方案，包含两大核心功能：

1. **EnhancedProcess** - 增强进程管理
   - 高级进程生命周期管理
   - 共享数据和队列支持
   - 异步/同步回调机制
   - 完善的错误处理和日志记录

2. **ProcessProxy** - 跨进程代理系统
   - 透明的跨进程对象访问
   - 高性能的共享内存机制
   - 自动错误处理和重试
   - 灵活的配置和监控

## 核心特性

### ProcessProxy 集成功能
EnhancedProcess 已完全集成 ProcessProxy 功能，提供简洁易用的跨进程对象访问能力：

- **便捷工厂方法**: `EnhancedProcess.create_with_proxy()`
- **工厂函数**: `create_proxy_process()`
- **装饰器支持**: `create_simple_proxy_worker()`
- **属性访问**: 通过 `proxy_accessor` 直接访问代理对象
- **错误处理**: 完善的异常处理和重试机制
- **性能优化**: 基于共享内存的高性能访问

### 基本使用示例

```python
from global_tools.utils.enhanced_process import EnhancedProcess

# 创建服务对象
class MyService:
    def __init__(self):
        self.counter = 0

    def increment(self, step=1):
        self.counter += step
        return self.counter

# 工作函数
def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    service = proxy_accessor.get_proxy('my_service')
    result = service.increment(5)
    return result

# 创建启用代理功能的进程
my_service = MyService()
process = EnhancedProcess.create_with_proxy(
    target=worker,
    proxy_objects={'my_service': my_service}
)

# 启动进程
process.start()
process.wait_for_completion()
```

## 导出的类和函数

### 核心类
- `EnhancedProcess`: 增强进程管理类
- `ProcessLogger`: 进程日志记录器
- `SharedDataManager`: 共享数据管理器

### 代理系统
- `ProcessProxyManager`: 代理管理器
- `ProcessProxy`: 代理对象
- `ProxyAccessor`: 代理访问器
- `ProxyConfiguration`: 代理配置

### 便捷函数
- `create_proxy_process()`: 创建代理进程的便捷函数
- `create_simple_proxy_worker()`: 装饰器工厂函数
- `create_proxy_manager()`: 创建代理管理器
- `create_proxy()`: 创建代理对象

更多详细信息请参考 README.md 文件和示例代码。
"""
from global_tools.utils import Logger, ClassInstanceManager, Colors, LogLevel

ClassInstanceManager.create_instance(Logger, "EnhancedProcessLogger")
logger: Logger = ClassInstanceManager.get_instance("EnhancedProcessLogger")
logger.set_instance_level(LogLevel.OFF)

# 为ProcessProxy模块创建日志记录器
ClassInstanceManager.create_instance(Logger, "ProcessProxyLogger")
proxy_logger: Logger = ClassInstanceManager.get_instance("ProcessProxyLogger")
proxy_logger.set_instance_level(LogLevel.OFF)

# 为ProxyAccessor模块创建日志记录器
ClassInstanceManager.create_instance(Logger, "ProxyAccessorLogger")
accessor_logger: Logger = ClassInstanceManager.get_instance("ProxyAccessorLogger")
accessor_logger.set_instance_level(LogLevel.OFF)

from .process import EnhancedProcess, ProcessLogger
from .shared_data import SharedDataManager
from .proxy_manager import (
    ProcessProxyManager,
    ProcessProxy,
    ProxyFactory,
    ProxyConfiguration,
    ErrorHandler,
    PerformanceMonitor,
    StateManager,
    CallDispatcher,
    CommandProcessor,
    create_proxy_manager,
    create_proxy
)
from .proxy_accessor import ProxyAccessor
from .proxy_config import (
    ProcessProxyConfig,
    create_default_config,
    create_debug_config,
    create_performance_config
)
from .decorators import (
    with_proxy_objects,
    process_proxy_enabled,
    with_proxy_config,
    ProcessProxyContext,
    extract_proxy_config
)
from .helper import (
    DataQueueHelper,
    SharedQueue,
    SharedQueueManager,
    create_proxy_process,
    create_simple_proxy_worker,
    extract_proxy_config_from_function
)

__all__ = [
    # 原有的增强进程类
    "EnhancedProcess",
    "ProcessLogger",
    "SharedDataManager",

    # 数据队列辅助类
    "DataQueueHelper",
    "SharedQueue",
    "SharedQueueManager",

    # 新增的进程代理类
    "ProcessProxyManager",
    "ProcessProxy",
    "ProxyFactory",
    "ProxyConfiguration",
    "ErrorHandler",
    "PerformanceMonitor",
    "StateManager",
    "CallDispatcher",
    "CommandProcessor",
    "ProxyAccessor",
    "ProcessProxyConfig",

    # 装饰器和上下文管理器
    "with_proxy_objects",
    "process_proxy_enabled",
    "with_proxy_config",
    "ProcessProxyContext",
    "extract_proxy_config",

    # 便捷函数
    "create_proxy_manager",
    "create_proxy",
    "create_default_config",
    "create_debug_config",
    "create_performance_config",
    "create_proxy_process",
    "create_simple_proxy_worker",
    "extract_proxy_config_from_function",
]
