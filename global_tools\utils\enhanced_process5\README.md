# EnhancedProcess 模块说明

本模块提供了增强的多进程控制与数据同步能力，包含 `EnhancedProcess` 和 `ProcessProxy` 两大核心功能。

## 核心功能

### 1. EnhancedProcess - 增强进程管理
- 高级进程生命周期管理
- 共享数据和队列支持
- 异步/同步回调机制
- 完善的错误处理和日志记录

### 2. ProcessProxy - 跨进程代理系统
- 透明的跨进程对象访问
- 高性能的共享内存机制
- 自动错误处理和重试
- 灵活的配置和监控

## ProcessProxy 集成功能

EnhancedProcess 已完全集成 ProcessProxy 功能，提供简洁易用的跨进程对象访问能力。

### 基本使用方式

#### 方式一：使用 create_with_proxy 工厂方法

```python
from global_tools.utils.enhanced_process import EnhancedProcess

# 创建要代理的服务对象
class MyService:
    def __init__(self):
        self.counter = 0

    def increment(self, step=1):
        self.counter += step
        return self.counter

# 工作函数 - 必须接受 proxy_accessor 参数
def worker_function(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    # 通过 proxy_accessor 获取代理对象
    service = proxy_accessor.get_proxy('my_service')

    # 调用代理对象的方法
    result = service.increment(5)
    print(f"计数器值: {result}")

    return result

# 创建服务实例
my_service = MyService()

# 使用工厂方法创建启用代理功能的进程
process = EnhancedProcess.create_with_proxy(
    target=worker_function,
    proxy_objects={
        'my_service': my_service
    }
)

# 启动进程并等待完成
process.start()
process.wait_for_completion()
```

#### 方式二：使用便捷工厂函数

```python
from global_tools.utils.enhanced_process import create_proxy_process

# 使用便捷函数创建进程
process = create_proxy_process(
    target=worker_function,
    proxy_objects={
        'my_service': my_service,
        'database': database_service
    }
)

process.start()
process.wait_for_completion()
```

#### 方式三：使用装饰器模式

```python
from global_tools.utils.enhanced_process import create_simple_proxy_worker

# 创建服务对象
my_service = MyService()
database = DatabaseService()

# 使用装饰器创建工作函数
@create_simple_proxy_worker({
    'service': my_service,
    'database': database
})
def my_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    # 使用代理对象
    result = proxy_accessor.service.increment()
    data = proxy_accessor.database.query('SELECT * FROM users')
    return result

# 创建并启动进程
process = EnhancedProcess(target=my_worker)
process.start()
```

### 高级功能

#### 获取代理管理器信息

```python
# 获取代理管理器连接信息
manager_info = process.get_proxy_manager_info()

# 在其他地方使用连接信息
from global_tools.utils.enhanced_process.proxy_accessor import ProxyAccessor
accessor = ProxyAccessor(manager_info)
service_proxy = accessor.get_proxy('my_service')
```

#### 属性访问方式

```python
def worker_with_attribute_access(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    # 方式1：通过 get_proxy 方法
    service = proxy_accessor.get_proxy('my_service')

    # 方式2：通过属性访问（更简洁）
    service = proxy_accessor.my_service

    # 两种方式等效
    result = service.increment(10)
    return result
```

#### 错误处理

```python
def robust_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    try:
        service = proxy_accessor.get_proxy('my_service')
        result = service.increment(5)
        return result
    except Exception as e:
        process_logger.error(f"代理调用失败: {e}")
        # 错误会自动输出详细堆栈信息
        return None
```

### 配置选项

#### 自定义代理配置

```python
from global_tools.utils.enhanced_process import ProxyConfiguration

# 创建自定义配置
config = ProxyConfiguration(
    timeout=30.0,           # 调用超时时间
    retry_count=3,          # 重试次数
    enable_monitoring=True, # 启用性能监控
    debug_mode=True         # 启用调试模式
)

# 使用自定义配置
process = EnhancedProcess.create_with_proxy(
    target=worker_function,
    proxy_objects={'service': my_service},
    proxy_config=config
)
```

### 最佳实践

1. **工作函数签名**：确保工作函数接受 `proxy_accessor` 参数
2. **对象生命周期**：代理对象在进程结束时自动清理
3. **错误处理**：所有代理调用都有完善的错误处理机制
4. **性能优化**：使用共享内存机制提供高性能访问
5. **调试支持**：启用调试模式获取详细的调用信息

### 注意事项

- 代理对象是独立的副本，修改不会影响原始对象
- 支持大部分 Python 对象类型，但有序列化限制
- 代理调用有网络开销，适合中等频率的调用
- 自动处理进程间通信的复杂性

## 传统用法示例

（以下内容保持原有的 EnhancedProcess 基础功能说明...）