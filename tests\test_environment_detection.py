# -*- coding: utf-8 -*-
"""
环境检测单元测试
================

本文件包含对环境感知系统中环境检测功能的详细单元测试，包括：
1. EnvironmentDetector 类的各种检测方法
2. 多层次环境检测逻辑
3. 环境检测的一致性验证
4. 缓存机制测试
5. 异常处理测试

使用方法：
    python -m pytest tests/test_environment_detection.py -v
"""

import unittest
import os
import sys
import time
import tempfile
import json
from unittest.mock import patch, MagicMock

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from global_tools.utils.enhanced_process.environment import (
    EnvironmentDetector,
    EnvironmentManager,
    SUPPORTED_ENVIRONMENTS,
    DEFAULT_ENVIRONMENT,
    ENVIRONMENT_VAR_NAME,
    CONFIG_FILE_NAME
)


class TestEnvironmentDetector(unittest.TestCase):
    """环境检测器测试类"""
    
    def setUp(self):
        """测试前的设置"""
        self.detector = EnvironmentDetector()
        self.original_env = os.environ.get(ENVIRONMENT_VAR_NAME)
        
    def tearDown(self):
        """测试后的清理"""
        # 恢复原始环境变量
        if self.original_env:
            os.environ[ENVIRONMENT_VAR_NAME] = self.original_env
        elif ENVIRONMENT_VAR_NAME in os.environ:
            del os.environ[ENVIRONMENT_VAR_NAME]
        
        # 清除检测器缓存
        self.detector.clear_cache()
    
    def test_environment_variable_detection(self):
        """测试环境变量检测"""
        # 测试有效的环境变量
        for env in SUPPORTED_ENVIRONMENTS:
            with self.subTest(environment=env):
                os.environ[ENVIRONMENT_VAR_NAME] = env
                self.detector.clear_cache()
                detected = self.detector.detect_environment()
                self.assertEqual(detected, env)
        
        # 测试无效的环境变量
        os.environ[ENVIRONMENT_VAR_NAME] = 'invalid_env'
        self.detector.clear_cache()
        detected = self.detector.detect_environment()
        self.assertEqual(detected, DEFAULT_ENVIRONMENT)
    
    def test_config_file_detection(self):
        """测试配置文件检测"""
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config = {
                'environment': 'production',
                'description': 'Test config'
            }
            json.dump(config, f)
            temp_config_path = f.name

        try:
            # 模拟配置文件解析器
            from global_tools.utils.enhanced_process.environment import ConfigFileParser
            with patch.object(ConfigFileParser, '_find_config_file', return_value=temp_config_path):
                self.detector.clear_cache()
                detected = self.detector.detect_environment()
                self.assertEqual(detected, 'production')
        finally:
            os.unlink(temp_config_path)
    
    def test_runtime_features_detection(self):
        """测试运行时特征检测"""
        # 测试调试器检测
        with patch('sys.gettrace', return_value=MagicMock()):
            self.detector.clear_cache()
            # 清除环境变量以确保使用运行时检测
            if ENVIRONMENT_VAR_NAME in os.environ:
                del os.environ[ENVIRONMENT_VAR_NAME]
            detected = self.detector._check_runtime_features()
            self.assertEqual(detected, 'development')
        
        # 测试容器环境检测
        with patch('os.path.exists', return_value=True) as mock_exists:
            mock_exists.side_effect = lambda path: path == '/.dockerenv'
            detected = self.detector._check_runtime_features()
            self.assertEqual(detected, 'production')
    
    def test_process_arguments_detection(self):
        """测试进程参数检测"""
        # 测试 --env 参数
        with patch('sys.argv', ['script.py', '--env', 'staging']):
            detected = self.detector._check_process_arguments()
            self.assertEqual(detected, 'staging')
        
        # 测试 --environment= 参数
        with patch('sys.argv', ['script.py', '--environment=testing']):
            detected = self.detector._check_process_arguments()
            self.assertEqual(detected, 'testing')
        
        # 测试 --debug 参数
        with patch('sys.argv', ['script.py', '--debug']):
            detected = self.detector._check_process_arguments()
            self.assertEqual(detected, 'development')
        
        # 测试 --production 参数
        with patch('sys.argv', ['script.py', '--production']):
            detected = self.detector._check_process_arguments()
            self.assertEqual(detected, 'production')
    
    def test_system_properties_detection(self):
        """测试系统属性检测"""
        # 测试主机名检测
        with patch('socket.gethostname', return_value='dev-server-01'):
            detected = self.detector._check_system_properties()
            self.assertEqual(detected, 'development')
        
        with patch('socket.gethostname', return_value='prod-web-server'):
            detected = self.detector._check_system_properties()
            self.assertEqual(detected, 'production')
        
        # 测试用户名检测
        with patch('getpass.getuser', return_value='developer'):
            with patch('socket.gethostname', return_value='normal-host'):
                detected = self.detector._check_system_properties()
                self.assertEqual(detected, 'development')
    
    def test_network_environment_detection(self):
        """测试网络环境检测"""
        # 测试代理环境变量
        original_proxy = os.environ.get('HTTP_PROXY')
        try:
            os.environ['HTTP_PROXY'] = 'http://proxy.company.com:8080'
            detected = self.detector._check_network_environment()
            self.assertEqual(detected, 'production')
        finally:
            if original_proxy:
                os.environ['HTTP_PROXY'] = original_proxy
            elif 'HTTP_PROXY' in os.environ:
                del os.environ['HTTP_PROXY']
        
        # 测试 Kubernetes 环境
        original_k8s = os.environ.get('KUBERNETES_SERVICE_HOST')
        try:
            os.environ['KUBERNETES_SERVICE_HOST'] = '********'
            detected = self.detector._check_network_environment()
            self.assertEqual(detected, 'production')
        finally:
            if original_k8s:
                os.environ['KUBERNETES_SERVICE_HOST'] = original_k8s
            elif 'KUBERNETES_SERVICE_HOST' in os.environ:
                del os.environ['KUBERNETES_SERVICE_HOST']
    
    def test_multi_layer_detection(self):
        """测试多层次检测"""
        # 清除环境变量
        if ENVIRONMENT_VAR_NAME in os.environ:
            del os.environ[ENVIRONMENT_VAR_NAME]
        
        self.detector.clear_cache()
        results = self.detector._perform_multi_layer_detection()
        
        # 验证返回的结果包含所有检测方法
        expected_keys = [
            'environment_variable',
            'config_file',
            'runtime_features',
            'process_arguments',
            'system_properties',
            'network_environment',
            'default_fallback'
        ]
        
        for key in expected_keys:
            self.assertIn(key, results)
    
    def test_best_environment_selection(self):
        """测试最佳环境选择"""
        # 模拟检测结果
        detection_results = {
            'environment_variable': 'production',
            'config_file': 'development',
            'runtime_features': None,
            'process_arguments': None,
            'system_properties': None,
            'network_environment': None,
            'default_fallback': 'development'
        }
        
        # 环境变量应该有最高优先级
        selected = self.detector._select_best_environment(detection_results)
        self.assertEqual(selected, 'production')
        
        # 测试没有环境变量的情况
        detection_results['environment_variable'] = None
        selected = self.detector._select_best_environment(detection_results)
        self.assertEqual(selected, 'development')
    
    def test_caching_mechanism(self):
        """测试缓存机制"""
        # 设置环境变量
        os.environ[ENVIRONMENT_VAR_NAME] = 'production'
        
        # 第一次检测
        start_time = time.time()
        env1 = self.detector.detect_environment()
        first_detection_time = time.time() - start_time
        
        # 第二次检测（应该使用缓存）
        start_time = time.time()
        env2 = self.detector.detect_environment()
        second_detection_time = time.time() - start_time
        
        # 验证结果一致
        self.assertEqual(env1, env2)
        self.assertEqual(env1, 'production')
        
        # 验证第二次检测更快（使用了缓存）
        self.assertLess(second_detection_time, first_detection_time)
    
    def test_cache_expiration(self):
        """测试缓存过期"""
        # 设置短的缓存超时时间
        original_timeout = self.detector._cache_timeout
        self.detector._cache_timeout = 0.1  # 100ms
        
        try:
            os.environ[ENVIRONMENT_VAR_NAME] = 'development'
            
            # 第一次检测
            env1 = self.detector.detect_environment()
            
            # 等待缓存过期
            time.sleep(0.2)
            
            # 更改环境变量
            os.environ[ENVIRONMENT_VAR_NAME] = 'production'
            
            # 第二次检测（缓存应该已过期）
            env2 = self.detector.detect_environment()
            
            self.assertEqual(env1, 'development')
            self.assertEqual(env2, 'production')
            
        finally:
            self.detector._cache_timeout = original_timeout
    
    def test_detection_details(self):
        """测试检测详情获取"""
        os.environ[ENVIRONMENT_VAR_NAME] = 'staging'
        
        # 执行检测
        self.detector.detect_environment()
        
        # 获取详情
        details = self.detector.get_detection_details()
        
        # 验证详情包含必要信息
        self.assertIn('current_environment', details)
        self.assertIn('detection_results', details)
        self.assertIn('detection_time', details)
        self.assertEqual(details['current_environment'], 'staging')
    
    def test_force_redetection(self):
        """测试强制重新检测"""
        os.environ[ENVIRONMENT_VAR_NAME] = 'development'
        
        # 第一次检测
        env1 = self.detector.detect_environment()
        
        # 更改环境变量
        os.environ[ENVIRONMENT_VAR_NAME] = 'production'
        
        # 普通检测（应该使用缓存）
        env2 = self.detector.detect_environment()
        
        # 强制重新检测
        env3 = self.detector.force_redetection()
        
        self.assertEqual(env1, 'development')
        self.assertEqual(env2, 'development')  # 使用缓存
        self.assertEqual(env3, 'production')  # 强制重新检测
    
    def test_consistency_validation(self):
        """测试一致性验证"""
        os.environ[ENVIRONMENT_VAR_NAME] = 'production'
        
        # 执行一致性验证
        consistency = self.detector.validate_environment_consistency()
        
        # 验证结果
        self.assertIn('is_consistent', consistency)
        self.assertIn('detection_results', consistency)
        self.assertIn('consistency_rate', consistency)
        self.assertTrue(consistency['is_consistent'])
        self.assertEqual(consistency['consistency_rate'], 1.0)
    
    def test_error_handling(self):
        """测试错误处理"""
        # 测试无效的配置文件
        with patch.object(self.detector, '_check_config_file', side_effect=Exception("Config error")):
            # 应该不会抛出异常，而是返回默认环境
            detected = self.detector.detect_environment()
            self.assertEqual(detected, DEFAULT_ENVIRONMENT)
    
    def test_supported_environments(self):
        """测试支持的环境列表"""
        # 验证所有支持的环境都能正确检测
        for env in SUPPORTED_ENVIRONMENTS:
            with self.subTest(environment=env):
                os.environ[ENVIRONMENT_VAR_NAME] = env
                self.detector.clear_cache()
                detected = self.detector.detect_environment()
                self.assertEqual(detected, env)


class TestEnvironmentManager(unittest.TestCase):
    """环境管理器测试类"""
    
    def setUp(self):
        """测试前的设置"""
        # 重置环境管理器（如果需要）
        pass
    
    def test_singleton_pattern(self):
        """测试单例模式"""
        manager1 = EnvironmentManager.get_instance()
        manager2 = EnvironmentManager.get_instance()
        
        # 验证是同一个实例
        self.assertIs(manager1, manager2)
    
    def test_environment_setting(self):
        """测试环境设置"""
        manager = EnvironmentManager.get_instance()
        
        # 测试设置有效环境
        for env in SUPPORTED_ENVIRONMENTS:
            with self.subTest(environment=env):
                success = manager.set_environment(env)
                self.assertTrue(success)
                self.assertEqual(manager.get_current_environment(), env)
        
        # 测试设置无效环境
        success = manager.set_environment('invalid_env')
        self.assertFalse(success)


if __name__ == '__main__':
    unittest.main()
