import sys
import os
import time
import multiprocessing
from typing import Dict, Any, List

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 导入 EnhancedProcess 相关模块
from global_tools.utils.enhanced_process import (
    EnhancedProcess,
    create_proxy_process,
    create_simple_proxy_worker,
    ProxyConfiguration,
    SharedDataManager,
    ProcessLogger,
    
)

class B:

    def __init__(self):
        self.name:str = None

    def b(self):
        self.name = "POP"
        print("输出日志：", self.name)

b = B()


class P:
    def A(self, *args):
        b.b(*args)

def error_handling_worker(shared_data_proxy:SharedDataManager, data_queue, process_logger:ProcessLogger, proxy_accessor):
    pass
    import time
    p = proxy_accessor.p
    
    p.A()
    

def main():
    p = P()
    process = EnhancedProcess.create_with_proxy(
        target=error_handling_worker,
        proxy_objects={'p': p}
    )

    process.start()
    process.wait_for_completion()
    print("b name：",b.name)
    

if __name__ == "__main__":
    main()
