# -*- coding: utf-8 -*-
"""
EnhancedProcess 辅助工具模块
==========================

本模块提供了 EnhancedProcess 的辅助工具类和便捷函数，包括：

## 核心工具类

### DataQueueHelper
用于 EnhancedProcess 子进程向主进程安全传递数据，并在主进程异步串行消费。

核心功能：
1. 子进程通过入队函数（enqueue_func）将数据发送到主进程（通过 multiprocessing.Queue）
2. 主进程异步、串行消费数据队列，callback 必须是异步函数，且不会阻塞主线程
3. 支持队列判空，确保所有数据消费完毕

### SharedQueue & SharedQueueManager
提供共享队列管理功能，支持多进程间的数据传递。

## ProcessProxy 便捷函数

### create_proxy_process()
创建启用代理功能的增强进程的便捷函数，简化了 EnhancedProcess.create_with_proxy 的使用。

### create_simple_proxy_worker()
创建简单的代理工作函数装饰器，自动处理代理对象的注入和错误处理。

### extract_proxy_config_from_function()
从函数中提取代理配置信息，支持装饰器模式的配置提取。

## 使用示例

### DataQueueHelper 基本用法
```python
# 1. 在主进程创建 DataQueueHelper
helper = DataQueueHelper()

# 2. 获取入队函数，传递给子进程
enqueue_func = helper.get_enqueue_func()

# 3. 在主进程注册异步消费回调
async def consume_callback(data):
    print(f"消费到数据: {data}")
helper.start_async_consume(consume_callback)

# 4. 在子进程中调用 enqueue_func(data) 发送数据

# 5. 在主进程 wait_queue_empty() 等待所有数据消费完毕
helper.wait_queue_empty()
```

### ProcessProxy 便捷函数用法
```python
# 使用便捷函数创建代理进程
process = create_proxy_process(
    target=worker_function,
    proxy_objects={'service': my_service}
)

# 使用装饰器模式
@create_simple_proxy_worker({'service': my_service})
def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    service = proxy_accessor.service
    return service.some_method()
```

详细注释见各方法说明。
"""
import multiprocessing
import queue
import threading
import asyncio
import time
from typing import Callable, Optional, Any, Coroutine
from multiprocessing.managers import BaseManager


class DataQueueHelper:
    """
    DataQueueHelper
    ---------------
    用于主进程和子进程间安全传递和异步消费数据。
    """

    def __init__(self):
        """
        初始化 DataQueueHelper。
        创建主进程本地队列和进程间队列，并启动转发线程。
        """
        self._mp_queue = multiprocessing.Queue()
        self._local_queue = queue.Queue()
        self._forward_thread = None
        self._consumer_thread = None
        self._async_callback = None
        self._loop = None
        self._consuming = threading.Event()
        self._consuming.set()
        self._forwarding = threading.Event()
        self._forwarding.set()
        self._forward_thread = threading.Thread(
            target=self._forward_mp_to_local, daemon=True)
        self._forward_thread.start()

    def get_enqueue_func(self) -> Callable[[Any], None]:
        """
        获取子进程用的数据入队函数。
        返回一个函数，调用时会将数据放入 multiprocessing.Queue，由主进程转发到本地队列。
        Returns:
            Callable[[Any], None]: 入队函数，参数为要发送的数据。
        用法示例：
            enqueue_func = helper.get_enqueue_func()
            # 在子进程中
            enqueue_func(data)
        """

        def enqueue(data):
            self._mp_queue.put(data)

        return enqueue

    def _forward_mp_to_local(self):
        """
        后台线程：不断从 mp_queue 取数据，放入本地队列。
        """
        while self._forwarding.is_set():
            try:
                data = self._mp_queue.get(timeout=0.1)
                self._local_queue.put(data)
            except queue.Empty:
                continue
            except Exception:
                break

    def start_async_consume(self, callback: Callable[[Any], Coroutine[Any, Any,
                                                                      None]]):
        """
        启动异步消费线程，串行消费本地队列数据。
        Args:
            callback (async function): 异步回调函数，参数为队列中的数据。
        用法示例：
            async def consume_cb(data):
                print(data)
            helper.start_async_consume(consume_cb)
        注意：
            - callback 必须是 async function。
            - 消费是串行的，callback 未返回前不会消费下一条。
        """
        if not asyncio.iscoroutinefunction(callback):
            raise TypeError("callback 必须为 async function")
        self._async_callback = callback
        try:
            self._loop = asyncio.get_running_loop()
        except RuntimeError:
            self._loop = asyncio.new_event_loop()
            threading.Thread(target=self._loop.run_forever,
                             daemon=True).start()
            time.sleep(0.05)  # 等待 loop 启动
        self._consumer_thread = threading.Thread(target=self._consume_loop,
                                                 daemon=True)
        self._consumer_thread.start()

    def _consume_loop(self):
        """
        后台线程：串行异步消费本地队列数据。
        """
        while self._consuming.is_set():
            try:
                data = self._local_queue.get(timeout=0.1)
            except queue.Empty:
                continue
            except Exception:
                break
            # 串行调度 async callback
            if self._async_callback is not None:
                fut = asyncio.run_coroutine_threadsafe(
                    self._async_callback(data), self._loop)
                try:
                    fut.result()  # 等待 callback 执行完成
                except Exception:
                    pass

    def wait_queue_empty(self, timeout: float = None, check_interval: float = 0.01) -> bool:
        """
        阻塞等待所有数据队列消费完毕。
        Args:
            timeout (float): 最长等待秒数，None表示无限等待。
            check_interval (float): 检查间隔秒数，默认 0.01。
        Returns:
            bool: True表示队列已清空，False表示超时。
            
        用法示例：
            if helper.wait_queue_empty(timeout=5):
                print("所有数据已处理完毕")
            else:
                print("等待超时，可能有数据未处理")
        """
        if timeout is not None:
            start_time = time.time()
            
        while not self._local_queue.empty() or not self._mp_queue.empty():
            if timeout is not None and (time.time() - start_time) > timeout:
                return False
            time.sleep(check_interval)
            
        # 等待消费线程处理完最后一条
        time.sleep(check_interval)
        return True

    def close(self):
        """
        关闭所有后台线程，释放资源。
        """
        self._consuming.clear()
        self._forwarding.clear()
        if self._forward_thread:
            self._forward_thread.join(timeout=1.0)
        if self._consumer_thread:
            self._consumer_thread.join(timeout=1.0)

    def get_mp_queue(self):
        """
        获取主进程和子进程共享的 multiprocessing.Queue。
        Returns:
            multiprocessing.Queue: 可用于跨进程 put 数据。
        用法示例：
            mp_queue = helper.get_mp_queue()
            # 子进程中 mp_queue.put(data)
        """
        return self._mp_queue


"""
使用说明：
---------
1. 主进程创建 DataQueueHelper 实例。
2. 通过 get_enqueue_func() 获取入队函数，传递给子进程。
3. 主进程调用 start_async_consume(callback) 注册异步消费回调。
4. 子进程通过入队函数发送数据。
5. 主进程调用 wait_queue_empty() 等待所有数据消费完毕。
6. 用完后调用 close() 释放资源。
"""

# =====================
# 共享队列代理机制
# =====================


class SharedQueue:
    """
    SharedQueue
    -----------
    封装 multiprocessing.Queue，支持多进程/多主机安全 put/get。
    """

    def __init__(self):
        from multiprocessing import Queue
        self._queue = Queue()

    def put(self, item):
        """
        向队列安全添加数据。
        Args:
            item: 任意可序列化对象。
        """
        self._queue.put(item)

    def get(self, timeout=None):
        """
        从队列安全获取数据。
        Args:
            timeout (float|None): 超时时间，None为阻塞。
        Returns:
            item: 队列中的数据。
        """
        return self._queue.get(timeout=timeout)

    def qsize(self):
        """
        获取队列当前长度。
        Returns:
            int: 队列长度。
        """
        return self._queue.qsize()

    def empty(self):
        """
        判断队列是否为空。
        Returns:
            bool: True为空。
        """
        return self._queue.empty()


class SharedQueueManager(BaseManager):
    """
    SharedQueueManager
    ------------------
    用于注册和管理 SharedQueue 的自定义 Manager 类。
    支持本地/远程多进程共享队列。
    """
    pass


SharedQueueManager.register('get_queue', SharedQueue)
"""
用法说明：
---------
# 主进程：
manager = SharedQueueManager(address=("127.0.0.1", 50000), authkey=b"queue")
manager.start()
queue = manager.get_queue()
queue.put("hello")

# 子进程/远程进程：
manager = SharedQueueManager(address=("127.0.0.1", 50000), authkey=b"queue")
manager.connect()
queue = manager.get_queue()
queue.put("world")

# 主进程消费：
print(queue.get())

详细注释见各方法说明。
"""


# =====================
# EnhancedProcess 便捷工厂函数
# =====================

def create_proxy_process(target: Callable, proxy_objects: dict, **kwargs):
    """
    创建启用代理功能的增强进程的便捷函数

    该函数是 EnhancedProcess.create_with_proxy 的便捷封装，
    简化了创建启用 ProcessProxy 功能的进程的代码。

    Args:
        target (Callable): 子进程执行的目标函数
        proxy_objects (dict): 预注册的代理对象字典，格式为 {obj_id: obj_instance}
        **kwargs: 其他传递给 EnhancedProcess 构造函数的参数

    Returns:
        EnhancedProcess: 配置好的 EnhancedProcess 实例

    使用示例：
        # 创建服务对象
        my_service = MyService()
        database = DatabaseService()

        # 使用便捷函数创建进程
        process = create_proxy_process(
            target=worker_function,
            proxy_objects={
                'service': my_service,
                'database': database
            }
        )

        # 启动进程
        process.start()
        process.wait_for_completion()
    """
    try:
        from .process import EnhancedProcess

        return EnhancedProcess.create_with_proxy(
            target=target,
            proxy_objects=proxy_objects,
            **kwargs
        )

    except Exception as e:
        import traceback
        print(f"[create_proxy_process] 创建代理进程失败: {e}")
        traceback.print_exc()
        raise


def create_simple_proxy_worker(proxy_objects: dict):
    """
    创建简单的代理工作函数装饰器

    该装饰器可以将普通函数转换为支持代理对象的工作函数，
    自动处理代理对象的注入和错误处理。

    Args:
        proxy_objects (dict): 预注册的代理对象字典，格式为 {obj_id: obj_instance}

    Returns:
        function: 装饰器函数

    使用示例：
        # 创建服务对象
        my_service = MyService()
        database = DatabaseService()

        # 使用装饰器创建工作函数
        @create_simple_proxy_worker({
            'service': my_service,
            'database': database
        })
        def my_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            # 使用代理对象
            result = proxy_accessor.service.increment()
            data = proxy_accessor.database.query('SELECT * FROM users')
            return result

        # 创建并启动进程
        process = EnhancedProcess(target=my_worker)
        process.start()
    """
    def decorator(func):
        """装饰器实现"""
        try:
            import functools

            # 为函数添加代理配置属性
            func._proxy_objects = proxy_objects.copy()
            func._proxy_enabled = True

            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)

            # 保持代理配置属性
            wrapper._proxy_objects = proxy_objects.copy()
            wrapper._proxy_enabled = True

            return wrapper

        except Exception as e:
            import traceback
            print(f"[create_simple_proxy_worker] 创建装饰器失败: {e}")
            traceback.print_exc()
            raise

    return decorator


def extract_proxy_config_from_function(func: Callable) -> tuple:
    """
    从函数中提取代理配置信息

    该函数检查目标函数是否包含代理配置属性，
    并返回相应的配置信息。

    Args:
        func (Callable): 要检查的函数

    Returns:
        tuple: (proxy_enabled, proxy_objects, proxy_config)
            - proxy_enabled (bool): 是否启用代理功能
            - proxy_objects (dict): 代理对象字典
            - proxy_config (ProxyConfiguration): 代理配置对象

    使用示例：
        enabled, objects, config = extract_proxy_config_from_function(worker_func)
        if enabled:
            process = EnhancedProcess.create_with_proxy(
                target=worker_func,
                proxy_objects=objects,
                proxy_config=config
            )
    """
    try:
        # 检查是否有代理配置属性
        proxy_enabled = getattr(func, '_proxy_enabled', False)
        proxy_objects = getattr(func, '_proxy_objects', {})
        proxy_config = getattr(func, '_proxy_config', None)

        return proxy_enabled, proxy_objects, proxy_config

    except Exception as e:
        import traceback
        print(f"[extract_proxy_config_from_function] 提取代理配置失败: {e}")
        traceback.print_exc()
        return False, {}, None
