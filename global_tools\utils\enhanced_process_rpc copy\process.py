import multiprocessing
import traceback
import time
from typing import Any, Callable, Optional
import queue
import threading
import random
from .shared_data import DataManagerManager, SharedDataManager  # 导入 SharedDataManager
from global_tools.utils import ClassInstanceManager, Colors, Logger
from .helper import DataQueueHelper, SharedQueueManager
import asyncio

logger: Logger = ClassInstanceManager.get_instance("EnhancedProcessLogger")


class ProcessLogger:
    """
    进程日志记录器，提供给子进程用于手动记录日志，并传递到主进程。
    提供与 SharedDataManager 类似的使用接口。
    
    用法示例：
    ----------
    def worker(shared_data_proxy, data_queue, process_logger, *args, **kwargs):
        # 方式一：使用标准参数记录不同级别的日志
        process_logger.log("这是一条信息日志")
        process_logger.log("这是一条警告日志", level="WARNING", color=Colors.WARNING)
        process_logger.log("这是一条错误日志", level="ERROR", color=Colors.ERROR)
        
        # 方式二：直接传递字典记录自定义数据
        process_logger.log({
            'message': '自定义日志内容',
            'level': 'DEBUG',
            'extra_field': '自定义字段',
            'data': {'key': 'value'}
        })
        
        # 正常使用print输出到控制台（不会传递到主进程回调）
        print("这条日志会输出到控制台，但不会传递到主进程")
    """

    def __init__(self, mp_queue):
        """
        初始化日志记录器
        Args:
            mp_queue (multiprocessing.Queue): 进程间共享的日志队列
        """
        self._mp_queue = mp_queue

    def log(self, message_or_dict, level="INFO", color=None):
        """
        记录一条日志，将被传递到主进程的回调函数
        
        支持两种调用方式：
        1. 标准参数: log(message, level, color)
        2. 字典参数: log(log_dict)
        
        Args:
            message_or_dict (str or dict): 
                - 字符串时为日志消息内容
                - 字典时为完整的日志条目，将直接传递给主进程
            level (str): 日志级别，如 "INFO", "WARNING", "ERROR" 等
            color: 可选的颜色信息
            
        Example:
            # 标准方式
            process_logger.log("操作成功", level="INFO")
            
            # 字典方式，可包含任意自定义字段
            process_logger.log({
                'message': '请求失败',
                'level': 'ERROR',
                'error_code': 404,
                'request_data': {'url': '/api/users'},
                'timestamp': '2025-06-12 15:30:45'
            })
        """
        if self._mp_queue is not None:
            if isinstance(message_or_dict, dict):
                # 如果传递的是字典，直接使用该字典作为日志条目
                log_entry = message_or_dict
                # 确保有timestamp字段
                if 'timestamp' not in log_entry:
                    log_entry['timestamp'] = time.strftime("%Y-%m-%d %H:%M:%S")
            else:
                # 传统方式：使用参数构建标准日志条目
                log_entry = {
                    'message': str(message_or_dict),
                    'level': level,
                    'color': color,
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
                }

            self._mp_queue.put(log_entry)


class EnhancedProcess:
    """
    增强的进程类，自动通过 DataManagerManager 代理的 SharedDataManager 进行多进程/多主机共享数据。

    新增功能：
    1. 支持子进程向主进程安全传递数据（通过 DataQueueHelper），主进程异步串行消费。
    2. 支持子进程手动记录日志并传递到主进程（通过 process_logger 参数）。
    3. 详见 set_data_consume_callback、wait_for_completion 方法说明。

    详细使用示例：
    ----------------
    # 1. 基础使用（向后兼容）
    def worker(shared_data_proxy, data_queue, process_logger, *args, **kwargs):
        # 在子进程中安全地操作共享数据
        with shared_data_proxy.get_lock():
            shared_data_proxy.set('key', 'auto')
            shared_data_proxy.append_to_list('numbers', 123)
            shared_data_proxy.add_to_set('tags', 'worker')

        # 通过 process_logger 记录日志（会被传递到主进程）
        process_logger.log("子进程执行中...", level="INFO")

        # 直接print输出不受影响，但不会被传递到主进程回调
        print("这条日志只会显示在控制台")

    # 2. ProcessProxy集成使用（新功能）
    def worker_with_proxy(shared_data_proxy, data_queue, process_logger, proxy_accessor, *args, **kwargs):
        # 使用代理对象访问主进程中的服务
        service = proxy_accessor.get_proxy('my_service')
        result = service.increment()

        # 或者使用属性访问方式
        database = proxy_accessor.database
        data = database.query('SELECT * FROM users')

        # 列出可用的代理对象
        available = proxy_accessor.list_available_objects()
        process_logger.log(f"可用代理对象: {available}")

        return result

    # 3. 基础使用（无需手动管理 manager/proxy）
    process1 = EnhancedProcess(target=worker)
    process2 = EnhancedProcess(target=worker)

    # 4. ProcessProxy集成使用
    # 创建要代理的服务对象
    my_service = MyService()
    database = DatabaseService()

    # 启用ProcessProxy功能并注册对象
    proxy_process = EnhancedProcess(
        target=worker_with_proxy,
        enable_proxy=True,
        proxy_objects={
            'my_service': my_service,
            'database': database
        }
    )

    # 5. 设置日志回调函数（处理通过 process_logger 记录的日志）
    def log_callback(log_entry):
        print(f"[{log_entry['level']}] {log_entry['message']}")
    process1.set_log_callback(log_callback)
    proxy_process.set_log_callback(log_callback)

    process1.start()
    process2.start()
    proxy_process.start()

    process1._process.join()
    process2._process.join()
    proxy_process._process.join()

    # 6. 在主进程中访问同一份共享数据
    shared_data_proxy = EnhancedProcess.get_shared_data_proxy()
    with shared_data_proxy.get_lock():
        print(shared_data_proxy.get('key'))           # 输出: auto
        print(shared_data_proxy.get_list('numbers'))  # 输出: [123, 123]
        print(shared_data_proxy.get_set('tags'))      # 输出: {'worker'}

    # 7. 检查代理功能状态
    if proxy_process.is_proxy_enabled():
        print(f"已注册的代理对象: {proxy_process.get_registered_proxy_objects()}")

    # 8. 资源释放（可选，自动管理）
    EnhancedProcess.shutdown_manager()

    # 说明：
    # - EnhancedProcess 会自动初始化和管理 DataManagerManager 及共享数据代理。
    # - 所有 EnhancedProcess 实例自动共享同一个共享数据代理。
    # - 所有数据操作建议加锁，保证多进程安全。
    # - 进程结束后可调用 EnhancedProcess.shutdown_manager() 释放资源。
    #
    # 新增参数说明：
    # - child_log_also_to_console (bool):
    #     此参数不再影响标准输出，仅作为兼容保留。
    #     子进程标准输出始终保持正常，不受影响。
    #
    # 用法示例：
    #   process = EnhancedProcess(target=worker)
    #   process.set_log_callback(log_cb)
    #   process.start()
    """

    _manager: Optional[DataManagerManager] = None
    _shared_data_proxy: Optional[SharedDataManager] = None
    _manager_lock = threading.Lock()

    def __init__(self,
                 target: Callable,
                 child_log_also_to_console: bool = False,
                 use_shared_queue_manager: bool = False,
                 shared_queue_manager_config: dict = None,
                 # 新增ProcessProxy相关参数
                 enable_proxy: bool = False,
                 proxy_config: 'ProxyConfiguration' = None,
                 proxy_objects: dict = None):
        """
        初始化增强进程。
        Args:
            target (Callable): 子进程执行的目标函数。
            child_log_also_to_console (bool):
                此参数不再影响标准输出，仅作为兼容保留。
                子进程标准输出始终保持正常，不受影响。
            use_shared_queue_manager (bool): 是否使用SharedQueueManager代理队列（适合多主机/分布式）。
            shared_queue_manager_config (dict): SharedQueueManager配置参数（如address, authkey, connect等）。
            enable_proxy (bool): 是否启用ProcessProxy功能，默认False保持向后兼容。
            proxy_config (ProxyConfiguration, optional): ProcessProxy配置对象，如果为None则使用默认配置。
            proxy_objects (dict, optional): 预注册的代理对象字典，格式为 {obj_id: obj_instance}。
        """
        if not callable(target):
            raise TypeError("target must be a callable")
        self.callback = target
        self._process = None
        self._internal_queue = multiprocessing.Queue()  # 内部通信队列
        self._data_queue_helper = None  # 延迟创建，只有在需要时才初始化
        self._data_queue = None  # 延迟创建，只有在需要时才初始化
        self._final_result = None
        self._error = None

        # ProcessProxy相关属性
        self._proxy_enabled = enable_proxy
        self._proxy_config = proxy_config
        self._proxy_manager = None  # 延迟初始化
        self._registered_proxy_objects = proxy_objects.copy() if proxy_objects else {}
        self._terminated = False
        self._final_data_received = False
        # 日志回调相关
        self._log_callback = None  # 日志回调函数
        self._is_async_log_callback = False  # 是否为异步回调
        self._log_queue = None  # 日志队列（主进程内）
        self._log_thread = None  # 日志处理线程
        self._log_callback_busy = threading.Event()  # 控制 callback 串行
        self._log_callback_busy.set()  # 初始为可用
        self._child_log_also_to_console = child_log_also_to_console  # 参数保留，但不再影响逻辑
        self._async_loop = None  # 异步事件循环
        # 自动初始化共享数据代理
        with EnhancedProcess._manager_lock:
            if EnhancedProcess._manager is None:
                EnhancedProcess._manager = DataManagerManager()
                EnhancedProcess._manager.start()
                EnhancedProcess._shared_data_proxy = EnhancedProcess._manager.SharedDataManager(
                )
        self._shared_data_proxy = EnhancedProcess._shared_data_proxy
        # 队列机制选择
        self._use_shared_queue_manager = use_shared_queue_manager
        self._shared_queue_manager = None
        self._data_consume_callback_set = False  # 标记是否设置了消费回调
        # 将队列创建逻辑移至 _init_data_queue 方法，只在需要时才初始化
        self._shared_queue_manager_config = shared_queue_manager_config
        # 日志：构造函数初始化
        logger.debug(
            f"[EnhancedProcess] 实例初始化，target={target}, child_log_also_to_console={child_log_also_to_console}",
            color=Colors.DEBUG)

    @staticmethod
    def get_shared_data_proxy():
        """
        获取全局共享数据代理对象。
        Returns:
            Any: 共享数据代理对象（manager.SharedDataManager() 返回值）
        """
        return EnhancedProcess._shared_data_proxy

    @staticmethod
    def shutdown_manager():
        """
        关闭全局 DataManagerManager 及其资源。
        """
        with EnhancedProcess._manager_lock:
            if EnhancedProcess._manager is not None:
                try:
                    EnhancedProcess._manager.shutdown()
                except Exception:
                    pass
                EnhancedProcess._manager = None
                EnhancedProcess._shared_data_proxy = None

    def start(self, *args, **kwargs) -> None:
        if self._process and self._process.is_alive():
            logger.warning("[EnhancedProcess] start: 进程已在运行，无法重复启动。",
                           color=Colors.WARNING)
            raise RuntimeError("Process is already running")
        self._final_result = None
        self._error = None
        self._terminated = False
        self._final_data_received = False
        # 日志回调机制
        if self._log_callback:
            import queue as pyqueue
            self._log_queue = pyqueue.Queue()
            self._mp_log_queue = multiprocessing.Queue()
            logger.info("[EnhancedProcess] 启动日志回调机制，已设置 log_callback。",
                        color=Colors.INFO)

            # 获取或创建事件循环（异步回调使用）
            if self._is_async_log_callback:
                try:
                    self._async_loop = asyncio.get_running_loop()
                except RuntimeError:
                    self._async_loop = asyncio.new_event_loop()
                    threading.Thread(target=self._async_loop.run_forever,
                                     daemon=True).start()
                    time.sleep(0.05)  # 等待事件循环启动

            # 启动日志处理线程
            def log_thread_func():
                while True:
                    try:
                        log = self._log_queue.get()
                        if log is None:
                            break

                        if self._is_async_log_callback and self._log_callback is not None:
                            # 异步回调：使用事件循环处理，不阻塞
                            asyncio.run_coroutine_threadsafe(
                                self._log_callback(log), self._async_loop)
                        elif callable(self._log_callback):
                            # 同步回调：使用事件等待确保串行处理
                            self._log_callback_busy.wait()  # 等待 callback 空闲
                            self._log_callback_busy.clear()
                            try:
                                self._log_callback(log)
                            finally:
                                self._log_callback_busy.set()
                    except Exception as e:
                        logger.error(f"[EnhancedProcess] 日志回调异常: {e}",
                                     color=Colors.ERROR)
                        self._log_callback_busy.set()

            self._log_thread = threading.Thread(target=log_thread_func,
                                                daemon=True)
            self._log_thread.start()

            # 启动日志转发线程（mp queue -> thread queue）
            def mp_log_forward_func():
                while True:
                    try:
                        log = self._mp_log_queue.get()
                        if log is None:
                            break
                        self._log_queue.put(log)
                    except Exception:
                        break

            self._mp_log_forward_thread = threading.Thread(
                target=mp_log_forward_func, daemon=True)
            self._mp_log_forward_thread.start()
            log_queue_arg = self._mp_log_queue
        else:
            log_queue_arg = None

        # ProcessProxy初始化
        proxy_manager_info = None
        if self._proxy_enabled:
            proxy_manager_info = self._init_proxy_manager()

        logger.info(
            f"[EnhancedProcess] 启动子进程，target={self.callback}, args={args}, kwargs={kwargs}, child_log_also_to_console={self._child_log_also_to_console}, proxy_enabled={self._proxy_enabled}",
            color=Colors.INFO)
        self._process = multiprocessing.Process(
            target=self._run_wrapper,
            args=(self.callback, self._internal_queue, self._shared_data_proxy,
                  self._data_queue, args, kwargs, log_queue_arg,
                  self._child_log_also_to_console, proxy_manager_info),
        )
        self._process.start()
        logger.info(f"[EnhancedProcess] 子进程已启动，pid={self._process.pid}",
                    color=Colors.INFO)

    @staticmethod
    def _run_wrapper(
            callback: Callable,
            internal_queue: multiprocessing.Queue,
            shared_data_proxy: Any,
            data_queue: multiprocessing.Queue,
            args: tuple,
            kwargs: dict,
            log_queue: Optional[multiprocessing.Queue] = None,
            child_log_also_to_console: bool = False,  # 保留参数但不使用
            proxy_manager_info: Optional[dict] = None,  # 新增ProcessProxy管理器信息
    ) -> None:
        import sys
        import io
        import os

        # 日志：子进程启动
        try:
            logger.info(
                f"[EnhancedProcess] 子进程(pid={os.getpid()}) 启动，callback={callback}",
                color=Colors.INFO)
        except Exception:
            pass

        # 创建进程日志记录器
        process_logger = ProcessLogger(log_queue) if log_queue else None

        # 创建ProcessProxy访问器
        proxy_accessor = None
        if proxy_manager_info:
            try:
                proxy_accessor = EnhancedProcess._create_proxy_accessor(proxy_manager_info)
                logger.info(f"[EnhancedProcess] 子进程(pid={os.getpid()}) ProcessProxy访问器已创建",
                           color=Colors.INFO)
            except Exception as e:
                logger.error(f"[EnhancedProcess] 子进程创建ProcessProxy访问器失败: {e}",
                           color=Colors.ERROR)
                import traceback
                traceback.print_exc()

        def send_current_state():
            try:
                internal_queue.put(("sync", None))
            except Exception as e:
                logger.error(f"[EnhancedProcess] 子进程发送状态时出错: {e}",
                             color=Colors.ERROR)

        def check_terminate():
            while not stop_event.is_set():
                try:
                    msg = internal_queue.get_nowait()
                    if msg == "terminate":
                        terminate_event.set()
                        send_current_state()
                        break
                except multiprocessing.queues.Empty:
                    time.sleep(0.05)
                except Exception as e:
                    logger.error(f"[EnhancedProcess] 子进程检查终止信号时出错: {e}",
                                 color=Colors.ERROR)
                    break

        stop_event = threading.Event()
        terminate_event = threading.Event()

        def status_updater():
            while not stop_event.is_set():
                send_current_state()
                time.sleep(0.1)

        status_thread = threading.Thread(target=status_updater, daemon=True)
        terminate_thread = threading.Thread(target=check_terminate,
                                            daemon=True)
        status_thread.start()
        terminate_thread.start()
        try:
            logger.info(
                f"[EnhancedProcess] 子进程(pid={os.getpid()}) 执行回调 {callback}",
                color=Colors.INFO)

            # 检测callback函数签名并决定是否传递proxy_accessor参数
            if proxy_accessor and EnhancedProcess._supports_proxy_accessor(callback):
                # 传递proxy_accessor作为第4个参数
                result = callback(shared_data_proxy, data_queue, process_logger, proxy_accessor,
                                  *args, **kwargs)
                logger.debug(f"[EnhancedProcess] 子进程使用ProcessProxy模式调用回调", color=Colors.DEBUG)
            else:
                # 传递process_logger作为额外参数给callback函数（向后兼容）
                result = callback(shared_data_proxy, data_queue, process_logger,
                                  *args, **kwargs)
                if proxy_accessor:
                    logger.debug(f"[EnhancedProcess] 子进程使用兼容模式调用回调（函数不支持proxy_accessor）", color=Colors.DEBUG)
            send_current_state()
            internal_queue.put(("result", result))
            logger.info(f"[EnhancedProcess] 子进程(pid={os.getpid()}) 回调执行完成",
                        color=Colors.SUCCESS)
        except Exception as e:
            error_info = {"error": str(e), "traceback": traceback.format_exc()}
            internal_queue.put(("error", error_info))
            logger.error(
                f"[EnhancedProcess] 子进程(pid={os.getpid()}) 回调异常: {e}\n{traceback.format_exc()}",
                color=Colors.ERROR)
        finally:
            send_current_state()
            time.sleep(0.2)
            stop_event.set()
            status_thread.join(timeout=1.0)
            terminate_thread.join(timeout=1.0)
            logger.info(f"[EnhancedProcess] 子进程(pid={os.getpid()}) 退出",
                        color=Colors.INFO)

    def terminate_gracefully(self,
                             timeout: float = None,
                             wait_log_queue: bool = True,
                             callback: Callable = None) -> bool:
        """
        以最快速度、立即停止子进程，优先优雅终止，但绝不拖延。
        Args:
            timeout (float): 等待子进程终止的最大秒数。
            wait_log_queue (bool): 是否等待日志队列清空（默认True）。
            callback (Callable): 可选的回调函数，在进程终止且日志队列清空后调用。
                                可以是同步函数或异步函数(async def)。
        Returns:
            bool: True 表示进程及日志处理已完成，False 表示超时或异常。
            
        用法示例：
            # 同步回调
            def on_terminate():
                print("进程已终止")
                
            # 异步回调
            async def async_on_terminate():
                await asyncio.sleep(0)  # 不阻塞的异步处理
                print("进程已异步终止")
                
            process.terminate_gracefully(timeout=2, callback=on_terminate)  # 或 async_on_terminate
        """
        if not self._process or not self._process.is_alive():
            logger.info("[EnhancedProcess] terminate_gracefully: 进程未运行或已结束。",
                        color=Colors.INFO)
            # 进程未运行，直接执行回调
            if callback:
                self._execute_callback(callback)
            return True
        try:
            logger.info(f"[EnhancedProcess] 请求立即终止子进程，pid={self._process.pid}",
                        color=Colors.INFO)
            self._internal_queue.put("terminate")
            # 步骤1：发优雅信号后只等极短时间（0.01秒）
            self._process.join(timeout=0.01)
            if not self._process.is_alive():
                logger.info(
                    f"[EnhancedProcess] 子进程已优雅退出，pid={self._process.pid}",
                    color=Colors.SUCCESS)
            else:
                # 步骤2：立即 terminate
                logger.warning(
                    f"[EnhancedProcess] 子进程未优雅退出，立即 terminate()，pid={self._process.pid}",
                    color=Colors.WARNING)
                self._process.terminate()
                self._process.join(timeout=0.01)
                if not self._process.is_alive():
                    logger.info(
                        f"[EnhancedProcess] 子进程 terminate 后已退出，pid={self._process.pid}",
                        color=Colors.SUCCESS)
                else:
                    # 步骤3：立即 kill
                    logger.error(
                        f"[EnhancedProcess] terminate 无效，立即 kill，pid={self._process.pid}",
                        color=Colors.ERROR)
                    try:
                        if hasattr(self._process, "kill"):
                            self._process.kill()
                            self._process.join(timeout=0.01)
                        else:
                            import signal
                            import os
                            os.kill(self._process.pid, signal.SIGKILL)
                            self._process.join(timeout=0.01)
                    except Exception as kill_err:
                        logger.error(
                            f"[EnhancedProcess] kill 进程时出错: {kill_err}",
                            color=Colors.ERROR)
            # 步骤4：循环检查，确保进程 dead
            start_time = time.time()
            while self._process.is_alive() and (time.time() -
                                                start_time) < timeout:
                logger.warning(
                    f"[EnhancedProcess] 进程仍未退出，等待中... pid={self._process.pid}",
                    color=Colors.WARNING)
                self._process.join(timeout=0.01)
            if self._process.is_alive():
                logger.error(
                    f"[EnhancedProcess] 进程无法终止，pid={self._process.pid}",
                    color=Colors.ERROR)
                return False
            # 清理队列资源（原逻辑）
            try:
                while True:
                    try:
                        self._internal_queue.get_nowait()
                    except (multiprocessing.queues.Empty, EOFError,
                            BrokenPipeError):
                        break
                    except Exception:
                        break
                if hasattr(self._internal_queue, "close"):
                    self._internal_queue.close()
                if hasattr(self._internal_queue, "join_thread"):
                    self._internal_queue.join_thread()
                if hasattr(self._internal_queue, "cancel_join_thread"):
                    try:
                        timeout_event = threading.Event()
                        timeout_thread = threading.Thread(
                            target=lambda: timeout_event.wait(0.5) or self.
                            _internal_queue.cancel_join_thread())
                        timeout_thread.daemon = True
                        timeout_thread.start()
                        timeout_thread.join(0.6)
                        timeout_event.set()
                    except Exception as e:
                        logger.error(f"[EnhancedProcess] 取消等待队列线程时出错: {e}",
                                     color=Colors.ERROR)
            except Exception as queue_err:
                logger.error(f"[EnhancedProcess] 清理队列资源时出错: {queue_err}",
                             color=Colors.ERROR)
            self._terminated = True
            # 日志队列等待逻辑
            if wait_log_queue and self._log_callback and self._log_queue is not None:
                import time as _time
                log_start = _time.time()
                while True:
                    log_queue_empty = self._log_queue.empty()
                    mp_log_queue_empty = True
                    if hasattr(self, '_mp_log_queue'
                               ) and self._mp_log_queue is not None:
                        try:
                            mp_log_queue_empty = self._mp_log_queue.empty()
                        except Exception:
                            mp_log_queue_empty = True
                    if log_queue_empty and mp_log_queue_empty:
                        break
                    if timeout is not None and (_time.time() -
                                                log_start) > timeout:
                        logger.warning("[EnhancedProcess] 日志队列等待超时，仍有日志未处理。",
                                       color=Colors.WARNING)
                        return False
                    _time.sleep(0.01)
            logger.info(
                f"[EnhancedProcess] 进程及日志处理已终止，pid={self._process.pid}",
                color=Colors.SUCCESS)

            # 执行回调函数
            if callback:
                self._execute_callback(callback)

            return True
        except Exception as e:
            logger.error(
                f"[EnhancedProcess] 终止进程时出错: {e}\n{traceback.format_exc()}",
                color=Colors.ERROR)
            traceback.print_exc()
            try:
                if self._process and self._process.is_alive():
                    self._process.terminate()
                    self._process.join(timeout=0.01)
                if hasattr(self._internal_queue, "close"):
                    try:
                        self._internal_queue.close()
                    except Exception:
                        pass
            except Exception:
                pass
            return False

    def _execute_callback(self, callback: Callable):
        """
        执行回调函数，支持同步和异步回调
        
        Args:
            callback (Callable): 回调函数，可以是同步或异步函数
        """
        if callback is None:
            return

        is_async = asyncio.iscoroutinefunction(callback)

        if is_async:
            # 异步回调：使用事件循环处理
            try:
                # 获取或创建事件循环
                try:
                    loop = asyncio.get_running_loop()
                except RuntimeError:
                    # 没有运行中的事件循环，创建一个新的
                    loop = asyncio.new_event_loop()
                    thread = threading.Thread(target=loop.run_forever,
                                              daemon=True)
                    thread.start()
                    time.sleep(0.05)  # 等待事件循环启动

                # 运行异步回调
                asyncio.run_coroutine_threadsafe(callback(), loop)
                logger.debug(f"[EnhancedProcess] 异步回调函数已调度执行: {callback}",
                             color=Colors.DEBUG)
            except Exception as e:
                logger.error(f"[EnhancedProcess] 执行异步回调函数时出错: {e}",
                             color=Colors.ERROR)
        else:
            # 同步回调：直接执行
            try:
                callback()
                logger.debug(f"[EnhancedProcess] 同步回调函数已执行: {callback}",
                             color=Colors.DEBUG)
            except Exception as e:
                logger.error(f"[EnhancedProcess] 执行同步回调函数时出错: {e}",
                             color=Colors.ERROR)

    def get_error(self) -> Optional[str]:
        return self._error

    def is_alive(self) -> bool:
        return bool(self._process and self._process.is_alive())

    def cleanup(self):
        try:
            if self._process and self._process.is_alive():
                logger.info(
                    f"[EnhancedProcess] cleanup: 终止仍在运行的子进程，pid={self._process.pid}",
                    color=Colors.INFO)
                self.terminate_gracefully()
        except BaseException as e:
            logger.error(f"[EnhancedProcess] cleanup: 终止进程异常: {e}",
                         color=Colors.ERROR)
        # 日志线程和队列资源释放
        try:
            if hasattr(self, '_log_queue') and self._log_queue is not None:
                self._log_queue.put(None)
                if hasattr(self,
                           '_log_thread') and self._log_thread is not None:
                    self._log_thread.join(timeout=1.0)
                self._log_queue = None
                self._log_thread = None
            if hasattr(self,
                       '_mp_log_queue') and self._mp_log_queue is not None:
                self._mp_log_queue.put(None)
                if hasattr(self, '_mp_log_forward_thread'
                           ) and self._mp_log_forward_thread is not None:
                    self._mp_log_forward_thread.join(timeout=1.0)
                self._mp_log_queue = None
                self._mp_log_forward_thread = None
            logger.info("[EnhancedProcess] cleanup: 日志线程和队列资源已释放。",
                        color=Colors.SUCCESS)
        except Exception as e:
            logger.error(f"[EnhancedProcess] cleanup: 日志资源释放异常: {e}",
                         color=Colors.ERROR)
            # 新增：关闭数据队列辅助器
            if self._data_consume_callback_set:
                if hasattr(self, '_data_queue_helper'
                           ) and self._data_queue_helper is not None:
                    self._data_queue_helper.close()
                # 停止共享队列消费线程
                if hasattr(self, '_use_shared_queue_manager'
                           ) and self._use_shared_queue_manager:
                    self._stop_shared_queue_consumer_thread()
                    # 尝试关闭共享队列管理器
                    if hasattr(self, '_shared_queue_manager'
                               ) and self._shared_queue_manager is not None:
                        try:
                            logger.info("[EnhancedProcess] cleanup: 关闭共享队列管理器",
                                        color=Colors.INFO)
                            if hasattr(self._shared_queue_manager, 'shutdown'):
                                self._shared_queue_manager.shutdown()
                        except Exception as e:
                            logger.error(
                                f"[EnhancedProcess] cleanup: 关闭共享队列管理器异常: {e}",
                                color=Colors.ERROR)
                        self._shared_queue_manager = None

    def __del__(self):
        try:
            self.cleanup()
            logger.info("[EnhancedProcess] __del__: 资源已清理。",
                        color=Colors.SUCCESS)
        except BaseException as e:
            logger.error(f"[EnhancedProcess] __del__: 清理资源异常: {e}",
                         color=Colors.ERROR)

    def wait_for_completion(self, timeout: Optional[float] = None) -> bool:
        """
        阻塞等待进程完成，并确保日志队列（如有）清空，数据队列消费完毕。
        Args:
            timeout (float|None): 最长等待时间（秒），None 表示无限等待。
        Returns:
            bool: True 表示进程及日志处理已完成，False 表示超时未完成。
        用法示例：
            process = EnhancedProcess(target=worker)
            process.set_data_consume_callback(cb)
            process.start()
            finished = process.wait_for_completion(timeout=10)
            if finished:
                print("进程及日志处理已完成")
            else:
                print("等待超时")
        """
        if not self._process:
            logger.info("[EnhancedProcess] wait_for_completion: 进程未启动。",
                        color=Colors.INFO)
            return True

        # 等待子进程结束
        start_time = time.time()
        self._process.join(timeout=timeout)
        if self._process.is_alive():
            logger.warning(
                "[EnhancedProcess] wait_for_completion: 进程未在超时时间内结束。",
                color=Colors.WARNING)
            return False

        elapsed = time.time() - start_time
        remaining_timeout = None if timeout is None else max(
            0, timeout - elapsed)

        # 若有日志回调，需等待日志队列清空
        if self._log_callback and (hasattr(self, '_log_queue')
                                   and self._log_queue is not None):
            import time as _time
            queue_start = _time.time()
            check_interval = 0.01  # 更频繁检查，提高响应性能

            while True:
                # 检查日志队列和进程间日志队列是否都清空
                log_queue_empty = self._log_queue.empty()
                mp_log_queue_empty = True
                if hasattr(self,
                           '_mp_log_queue') and self._mp_log_queue is not None:
                    try:
                        mp_log_queue_empty = self._mp_log_queue.empty()
                    except Exception:
                        mp_log_queue_empty = True

                if log_queue_empty and mp_log_queue_empty:
                    break

                if remaining_timeout is not None and (
                        _time.time() - queue_start) > remaining_timeout:
                    logger.warning(
                        "[EnhancedProcess] wait_for_completion: 日志队列等待超时，仍有日志未处理。",
                        color=Colors.WARNING)
                    return False

                _time.sleep(check_interval)

            elapsed = time.time() - start_time
            remaining_timeout = None if timeout is None else max(
                0, timeout - elapsed)

        # 新增：等待数据队列消费完毕
        if self._data_consume_callback_set:
            if self._data_queue_helper:
                if not self._data_queue_helper.wait_queue_empty(
                        timeout=remaining_timeout):
                    logger.warning(
                        "[EnhancedProcess] wait_for_completion: 数据队列等待超时，仍有数据未处理。",
                        color=Colors.WARNING)
                    return False
            elif self._use_shared_queue_manager and hasattr(
                    self, '_data_queue'):
                # 等待共享队列消费完毕
                import time as _time
                queue_start = _time.time()
                check_interval = 0.01  # 更频繁检查，提高响应性能

                # 尝试等待队列为空
                try:
                    while True:
                        try:
                            if self._data_queue.empty():
                                break
                        except (EOFError, AttributeError,
                                BrokenPipeError) as e:
                            logger.warning(
                                f"[EnhancedProcess] 检查共享队列为空时出错: {e}",
                                color=Colors.WARNING)
                            break

                        if remaining_timeout is not None and (
                                _time.time() -
                                queue_start) > remaining_timeout:
                            logger.warning(
                                "[EnhancedProcess] wait_for_completion: 共享队列等待超时，仍有数据未处理。",
                                color=Colors.WARNING)
                            return False

                        _time.sleep(check_interval)

                    # 额外等待一下，确保最后一条数据处理完毕
                    _time.sleep(check_interval)
                except Exception as e:
                    logger.error(f"[EnhancedProcess] 等待共享队列消费完毕时出错: {e}",
                                 color=Colors.ERROR)

        logger.info("[EnhancedProcess] wait_for_completion: 进程及日志处理全部完成。",
                    color=Colors.SUCCESS)
        return True

    def set_log_callback(self, callback):
        """
        设置日志回调函数。回调函数接收一个日志条目字典。
        支持同步或异步回调函数。
        
        Args:
            callback (callable): 日志处理回调函数，接收子进程通过process_logger记录的日志。
                可以是普通函数或异步函数(async def)。
            
        用法示例：
            # 同步回调
            def log_cb(log_entry):
                print(f"[{log_entry['level']}] {log_entry['message']}")
            
            # 异步回调
            async def async_log_cb(log_entry):
                await asyncio.sleep(0)  # 不阻塞的异步处理
                print(f"[{log_entry['level']}] {log_entry['message']}")
            
            process = EnhancedProcess(target=worker)
            process.set_log_callback(async_log_cb)  # 或 log_cb
            process.start()
            
        注意：
            - callback 必须是线程安全的。
            - 异步回调会在事件循环中串行执行，性能更优，不阻塞主线程。
            - 日志会保存在队列中，按顺序处理，不会丢失。
            - 子进程的标准输出/错误会直接显示在控制台，不会传递到此回调。
        """
        if callback is not None and not callable(callback):
            logger.error(
                "[EnhancedProcess] set_log_callback: callback 必须为可调用对象或 None",
                color=Colors.ERROR)
            raise TypeError("callback 必须为可调用对象或 None")

        self._log_callback = callback
        self._is_async_log_callback = asyncio.iscoroutinefunction(callback)

        logger.info(
            f"[EnhancedProcess] set_log_callback: 设置{'异步' if self._is_async_log_callback else '同步'}日志回调函数 {callback}",
            color=Colors.INFO)

    def _create_shared_queue_consumer_thread(self, callback):
        """
        创建共享队列管理器的消费线程。
        Args:
            callback (async function): 异步回调函数，参数为队列中的数据。
        """
        import asyncio
        import queue as pyqueue

        # 确保有事件循环
        try:
            self._async_loop = asyncio.get_running_loop()
        except RuntimeError:
            self._async_loop = asyncio.new_event_loop()
            threading.Thread(target=self._async_loop.run_forever,
                             daemon=True).start()
            time.sleep(0.05)  # 等待事件循环启动

        # 创建消费者线程
        self._consumer_running = threading.Event()
        self._consumer_running.set()

        def consumer_thread_func():
            while self._consumer_running.is_set():
                try:
                    # 尝试从队列获取数据，带超时
                    try:
                        data = self._data_queue.get(timeout=0.1)
                    except (pyqueue.Empty, EOFError):
                        continue
                    except Exception as e:
                        logger.error(f"[EnhancedProcess] 共享队列获取数据错误: {e}",
                                     color=Colors.ERROR)
                        continue

                    # 异步调度回调
                    fut = asyncio.run_coroutine_threadsafe(
                        callback(data), self._async_loop)
                    try:
                        # 等待回调执行完成，确保串行处理
                        fut.result()
                    except Exception as e:
                        logger.error(f"[EnhancedProcess] 共享队列消费回调错误: {e}",
                                     color=Colors.ERROR)
                except Exception as e:
                    logger.error(f"[EnhancedProcess] 共享队列消费线程异常: {e}",
                                 color=Colors.ERROR)

        self._consumer_thread = threading.Thread(target=consumer_thread_func,
                                                 daemon=True)
        self._consumer_thread.start()
        logger.info("[EnhancedProcess] 共享队列消费线程已启动", color=Colors.SUCCESS)

    def _stop_shared_queue_consumer_thread(self):
        """
        停止共享队列管理器的消费线程。
        """
        if hasattr(self,
                   '_consumer_running') and self._consumer_running is not None:
            self._consumer_running.clear()
            if hasattr(
                    self,
                    '_consumer_thread') and self._consumer_thread is not None:
                self._consumer_thread.join(timeout=1.0)
                self._consumer_thread = None
            self._consumer_running = None
            logger.info("[EnhancedProcess] 共享队列消费线程已停止", color=Colors.SUCCESS)

    def _init_data_queue(self):
        """
        延迟初始化数据队列，只在需要时才创建。
        在 set_data_consume_callback 或其他需要使用队列的地方调用。
        """
        if self._data_queue is None:
            if self._use_shared_queue_manager:
                # 用SharedQueueManager作为业务数据队列
                config = self._shared_queue_manager_config or {}
                self._shared_queue_manager = SharedQueueManager(
                    address=config.get('address', ('127.0.0.1', 50000)),
                    authkey=config.get('authkey', b'queue'))
                if config.get('connect', False):
                    self._shared_queue_manager.connect()
                else:
                    self._shared_queue_manager.start()
                self._data_queue = self._shared_queue_manager.get_queue()
            else:
                # 默认本地DataQueueHelper作为业务数据队列
                self._data_queue_helper = DataQueueHelper()
                self._data_queue = self._data_queue_helper.get_mp_queue()
            logger.debug("[EnhancedProcess] 延迟初始化数据队列完成", color=Colors.DEBUG)

    def set_data_consume_callback(self, callback):
        """
        注册异步消费回调函数，仅消费业务数据队列。
        Args:
            callback (async function): 异步回调函数，参数为队列中的数据。
        用法示例：
            async def consume_cb(data):
                print(f"主进程消费到: {data}")
            process.set_data_consume_callback(consume_cb)
        注意：
            - callback 必须是 async function。
            - 消费是串行的，callback 未返回前不会消费下一条。
        """
        # 检查回调是否为协程函数
        if not asyncio.iscoroutinefunction(callback):
            raise TypeError("callback 必须为 async function")

        # 延迟初始化数据队列
        if self._data_queue is None:
            self._init_data_queue()

        # 根据队列类型选择消费机制
        if self._data_queue_helper:
            # 本地队列：使用 DataQueueHelper 消费
            self._data_queue_helper.start_async_consume(callback)
        elif self._use_shared_queue_manager:
            # 共享队列：创建专用消费线程
            self._create_shared_queue_consumer_thread(callback)
        else:
            logger.error("[EnhancedProcess] 队列机制未初始化，无法注册消费回调",
                         color=Colors.ERROR)
            raise RuntimeError("队列机制未初始化，无法注册消费回调")

        # 设置标志位，表示已设置消费回调
        self._data_consume_callback_set = True
        logger.debug("[EnhancedProcess] 已设置数据消费回调", color=Colors.DEBUG)

    # =====================
    # ProcessProxy集成方法
    # =====================

    def register_proxy_object(self, obj_id: str, obj_instance: Any):
        """
        注册代理对象，使其可以在子进程中访问

        Args:
            obj_id (str): 对象ID，用于在子进程中引用该对象
            obj_instance (Any): 要注册的对象实例

        Raises:
            ValueError: 如果obj_id已存在或obj_instance为None
            RuntimeError: 如果ProcessProxy功能未启用

        使用示例：
            process = EnhancedProcess(target=worker, enable_proxy=True)
            service = MyService()
            process.register_proxy_object('service', service)
            process.start()
        """
        if not self._proxy_enabled:
            raise RuntimeError("ProcessProxy功能未启用，请在初始化时设置 enable_proxy=True")

        if not obj_id:
            raise ValueError("obj_id 不能为空")
        if obj_instance is None:
            raise ValueError("obj_instance 不能为None")
        if obj_id in self._registered_proxy_objects:
            raise ValueError(f"对象ID '{obj_id}' 已存在")

        self._registered_proxy_objects[obj_id] = obj_instance

        # 如果代理管理器已初始化，立即注册
        if self._proxy_manager is not None:
            self._proxy_manager.register_object(obj_id, obj_instance)

        logger.info(f"[EnhancedProcess] 代理对象已注册: {obj_id} -> {type(obj_instance).__name__}",
                   color=Colors.INFO)

    def unregister_proxy_object(self, obj_id: str):
        """
        注销代理对象

        Args:
            obj_id (str): 要注销的对象ID

        Raises:
            KeyError: 如果obj_id不存在
            RuntimeError: 如果ProcessProxy功能未启用

        使用示例：
            process.unregister_proxy_object('service')
        """
        if not self._proxy_enabled:
            raise RuntimeError("ProcessProxy功能未启用")

        if obj_id not in self._registered_proxy_objects:
            raise KeyError(f"对象ID '{obj_id}' 不存在")

        # 从注册表中移除
        del self._registered_proxy_objects[obj_id]

        # 如果代理管理器已初始化，同时注销
        if self._proxy_manager is not None:
            self._proxy_manager.unregister_object(obj_id)

        logger.info(f"[EnhancedProcess] 代理对象已注销: {obj_id}", color=Colors.INFO)

    def get_proxy_manager(self):
        """
        获取代理管理器实例

        Returns:
            ProcessProxyManager: 代理管理器实例，如果未启用则返回None

        使用示例：
            manager = process.get_proxy_manager()
            if manager:
                print(f"代理管理器地址: {manager.address}")
        """
        return self._proxy_manager

    def is_proxy_enabled(self) -> bool:
        """
        检查ProcessProxy功能是否启用

        Returns:
            bool: 是否启用ProcessProxy功能

        使用示例：
            if process.is_proxy_enabled():
                process.register_proxy_object('service', service)
        """
        return self._proxy_enabled

    def get_registered_proxy_objects(self) -> list:
        """
        获取已注册的代理对象列表

        Returns:
            list: 已注册的对象ID列表

        使用示例：
            objects = process.get_registered_proxy_objects()
            print(f"已注册对象: {objects}")
        """
        return list(self._registered_proxy_objects.keys())

    def _init_proxy_manager(self) -> dict:
        """
        初始化ProcessProxy管理器（私有方法）

        Returns:
            dict: 包含代理管理器连接信息的字典
        """
        try:
            # 导入ProcessProxy相关类
            from .proxy_manager import ProcessProxyManager, ProxyConfiguration

            # 创建配置
            if self._proxy_config is None:
                self._proxy_config = ProxyConfiguration()

            # 创建代理管理器
            self._proxy_manager = ProcessProxyManager(config=self._proxy_config)
            self._proxy_manager.start()

            # 注册所有预设的代理对象
            for obj_id, obj_instance in self._registered_proxy_objects.items():
                self._proxy_manager.register_object(obj_id, obj_instance)

            # 准备连接信息
            proxy_manager_info = {
                'address': self._proxy_manager.address,
                'authkey': self._proxy_manager._authkey,
                'registered_objects': list(self._registered_proxy_objects.keys())
            }

            logger.info(f"[EnhancedProcess] ProcessProxy管理器已初始化，地址: {self._proxy_manager.address}",
                       color=Colors.SUCCESS)

            return proxy_manager_info

        except Exception as e:
            logger.error(f"[EnhancedProcess] 初始化ProcessProxy管理器失败: {e}", color=Colors.ERROR)
            import traceback
            traceback.print_exc()
            raise

    @staticmethod
    def _create_proxy_accessor(proxy_manager_info: dict):
        """
        创建ProcessProxy访问器（静态方法，在子进程中调用）

        Args:
            proxy_manager_info (dict): 代理管理器连接信息

        Returns:
            ProxyAccessor: 代理访问器实例
        """
        try:
            # 导入ProxyAccessor类
            from .proxy_accessor import ProxyAccessor

            # 创建代理访问器
            proxy_accessor = ProxyAccessor(proxy_manager_info)

            return proxy_accessor

        except ImportError as e:
            logger.error(f"[EnhancedProcess] 导入ProxyAccessor失败: {e}", color=Colors.ERROR)
            raise
        except Exception as e:
            logger.error(f"[EnhancedProcess] 创建ProxyAccessor失败: {e}", color=Colors.ERROR)
            raise

    @staticmethod
    def _supports_proxy_accessor(callback: Callable) -> bool:
        """
        检测回调函数是否支持proxy_accessor参数（静态方法）

        Args:
            callback (Callable): 回调函数

        Returns:
            bool: 是否支持proxy_accessor参数
        """
        try:
            import inspect

            # 获取函数签名
            sig = inspect.signature(callback)
            params = list(sig.parameters.keys())

            # 检查是否有至少4个参数
            if len(params) < 4:
                return False

            # 检查第4个参数名是否暗示支持代理功能
            proxy_param_names = ['proxy_accessor', 'proxy', 'proxies', 'object_proxy', 'proxy_manager']
            fourth_param = params[3]

            return fourth_param in proxy_param_names

        except Exception as e:
            logger.debug(f"[EnhancedProcess] 检测函数签名失败: {e}", color=Colors.DEBUG)
            return False
