#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EnhancedProcess ProcessProxy 集成功能示例
======================================

本文件提供了 EnhancedProcess 与 ProcessProxy 集成功能的完整使用示例，
展示了各种使用方式、配置选项和最佳实践。

示例包括：
1. 基本使用示例
2. 便捷工厂函数示例
3. 装饰器模式示例
4. 高级配置示例
5. 错误处理示例
6. 性能优化示例

作者：Augment Agent
版本：1.0.0
"""

import sys
import os
import time
import multiprocessing
from typing import Dict, Any, List

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 导入 EnhancedProcess 相关模块
from global_tools.utils.enhanced_process import (
    EnhancedProcess,
    create_proxy_process,
    create_simple_proxy_worker,
    ProxyConfiguration
)


# =====================
# 示例服务类定义
# =====================

class CounterService:
    """
    计数器服务 - 演示基本的状态管理
    """
    
    def __init__(self, initial_value: int = 0):
        """初始化计数器服务"""
        self.value = initial_value
        self.operation_count = 0
        self.history = []
        
        print(f"[主进程 {os.getpid()}] CounterService 已创建，初始值: {initial_value}")
    
    def increment(self, step: int = 1) -> int:
        """增加计数器"""
        old_value = self.value
        self.value += step
        self.operation_count += 1
        
        operation = f"increment({step}): {old_value} -> {self.value}"
        self.history.append(operation)
        
        print(f"[进程 {os.getpid()}] {operation}")
        return self.value
    
    def decrement(self, step: int = 1) -> int:
        """减少计数器"""
        old_value = self.value
        self.value -= step
        self.operation_count += 1
        
        operation = f"decrement({step}): {old_value} -> {self.value}"
        self.history.append(operation)
        
        print(f"[进程 {os.getpid()}] {operation}")
        return self.value
    
    def get_status(self) -> Dict[str, Any]:
        """获取计数器状态"""
        return {
            'value': self.value,
            'operation_count': self.operation_count,
            'history_count': len(self.history)
        }


class DatabaseService:
    """
    数据库服务 - 演示复杂对象的代理
    """
    
    def __init__(self):
        """初始化数据库服务"""
        self.data = {}
        self.query_count = 0
        
        print(f"[主进程 {os.getpid()}] DatabaseService 已创建")
    
    def store(self, key: str, value: Any) -> bool:
        """存储数据"""
        self.data[key] = value
        print(f"[进程 {os.getpid()}] 存储数据: {key} = {value}")
        return True
    
    def query(self, key: str) -> Any:
        """查询数据"""
        self.query_count += 1
        value = self.data.get(key)
        print(f"[进程 {os.getpid()}] 查询数据: {key} -> {value}")
        return value
    
    def list_keys(self) -> List[str]:
        """列出所有键"""
        keys = list(self.data.keys())
        print(f"[进程 {os.getpid()}] 数据键列表: {keys}")
        return keys
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'data_count': len(self.data),
            'query_count': self.query_count
        }


# =====================
# 工作函数定义
# =====================

def basic_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    """
    基本工作函数 - 演示基础代理功能
    """
    print(f"[子进程 {os.getpid()}] 基本工作函数开始")
    
    try:
        # 获取计数器服务代理
        counter = proxy_accessor.get_proxy('counter')
        
        # 执行一些操作
        counter.increment(5)
        counter.increment(3)
        counter.decrement(2)
        
        # 获取最终状态
        status = counter.get_status()
        print(f"[子进程 {os.getpid()}] 计数器最终状态: {status}")
        
        return status
        
    except Exception as e:
        print(f"[子进程 {os.getpid()}] 基本工作函数异常: {e}")
        return None


def database_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    """
    数据库工作函数 - 演示复杂对象代理
    """
    print(f"[子进程 {os.getpid()}] 数据库工作函数开始")
    
    try:
        # 通过属性访问获取服务代理
        db = proxy_accessor.database
        counter = proxy_accessor.counter
        
        # 存储一些数据
        db.store('user_1', {'name': 'Alice', 'age': 25})
        db.store('user_2', {'name': 'Bob', 'age': 30})
        
        # 查询数据
        user1 = db.query('user_1')
        user2 = db.query('user_2')
        
        # 更新计数器
        counter.increment(len(db.list_keys()))
        
        # 获取统计信息
        db_stats = db.get_stats()
        counter_stats = counter.get_status()
        
        result = {
            'users': [user1, user2],
            'db_stats': db_stats,
            'counter_stats': counter_stats
        }
        
        print(f"[子进程 {os.getpid()}] 数据库操作完成: {result}")
        return result
        
    except Exception as e:
        print(f"[子进程 {os.getpid()}] 数据库工作函数异常: {e}")
        return None


def error_handling_worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    """
    错误处理工作函数 - 演示错误处理机制
    """
    print(f"[子进程 {os.getpid()}] 错误处理工作函数开始")
    
    try:
        # 正常操作
        counter = proxy_accessor.get_proxy('counter')
        counter.increment(1)
        
        # 尝试访问不存在的代理对象
        try:
            nonexistent = proxy_accessor.get_proxy('nonexistent')
            nonexistent.some_method()
        except Exception as e:
            print(f"[子进程 {os.getpid()}] 预期的错误: {e}")
        
        # 继续正常操作
        final_status = counter.get_status()
        print(f"[子进程 {os.getpid()}] 错误处理后状态: {final_status}")
        
        return final_status
        
    except Exception as e:
        print(f"[子进程 {os.getpid()}] 错误处理工作函数异常: {e}")
        return None


# =====================
# 示例函数
# =====================

def example_1_basic_usage():
    """
    示例1：基本使用方式
    """
    print("\n" + "=" * 60)
    print("示例1：基本使用方式")
    print("=" * 60)
    
    # 创建服务对象
    counter = CounterService(initial_value=10)
    
    # 使用 create_with_proxy 创建进程
    process = EnhancedProcess.create_with_proxy(
        target=basic_worker,
        proxy_objects={'counter': counter}
    )
    
    # 启动并等待完成
    print(f"[主进程 {os.getpid()}] 启动进程...")
    process.start()
    success = process.wait_for_completion(timeout=10)
    
    if success:
        print(f"✅ 示例1完成，进程执行成功")
    else:
        print(f"❌ 示例1失败，进程执行超时")
    
    return success


def example_2_factory_function():
    """
    示例2：便捷工厂函数
    """
    print("\n" + "=" * 60)
    print("示例2：便捷工厂函数")
    print("=" * 60)
    
    # 创建服务对象
    counter = CounterService(initial_value=20)
    database = DatabaseService()
    
    # 使用便捷工厂函数
    process = create_proxy_process(
        target=database_worker,
        proxy_objects={
            'counter': counter,
            'database': database
        }
    )
    
    # 启动并等待完成
    print(f"[主进程 {os.getpid()}] 启动进程...")
    process.start()
    success = process.wait_for_completion(timeout=10)
    
    if success:
        print(f"✅ 示例2完成，进程执行成功")
    else:
        print(f"❌ 示例2失败，进程执行超时")
    
    return success


# 全局装饰器工作函数（避免 pickle 问题）
@create_simple_proxy_worker({})  # 空字典，稍后动态设置
def decorated_worker_global(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    """全局装饰器工作函数"""
    print(f"[子进程 {os.getpid()}] 装饰器工作函数开始")

    # 使用代理对象
    counter_proxy = proxy_accessor.counter
    counter_proxy.increment(7)
    counter_proxy.decrement(2)

    status = counter_proxy.get_status()
    print(f"[子进程 {os.getpid()}] 装饰器工作完成: {status}")
    return status


def example_3_decorator_pattern():
    """
    示例3：装饰器模式（简化版）
    """
    print("\n" + "=" * 60)
    print("示例3：装饰器模式（简化版）")
    print("=" * 60)

    # 创建服务对象
    counter = CounterService(initial_value=30)

    # 由于 pickle 限制，我们使用传统方式但展示装饰器概念
    print(f"[主进程 {os.getpid()}] 注意：由于 multiprocessing 的 pickle 限制，")
    print(f"[主进程 {os.getpid()}] 装饰器模式在实际使用中需要定义为全局函数")

    # 使用传统方式创建进程
    process = EnhancedProcess.create_with_proxy(
        target=decorated_worker_global,
        proxy_objects={'counter': counter}
    )

    print(f"[主进程 {os.getpid()}] 启动装饰器风格进程...")
    process.start()
    success = process.wait_for_completion(timeout=10)

    if success:
        print(f"✅ 示例3完成，装饰器风格成功")
    else:
        print(f"❌ 示例3失败，装饰器风格超时")

    return success


def example_4_advanced_configuration():
    """
    示例4：高级配置选项
    """
    print("\n" + "=" * 60)
    print("示例4：高级配置选项")
    print("=" * 60)

    # 创建服务对象
    counter = CounterService(initial_value=40)

    # 创建自定义配置
    config = ProxyConfiguration()
    config.queue_timeout = 30.0        # 队列超时时间
    config.max_retries = 3              # 最大重试次数
    config.performance_monitoring = True # 启用性能监控
    config.debug_mode = True            # 启用调试模式
    config.optimize_for_scenario('debug')  # 优化为调试场景

    # 使用自定义配置创建进程
    process = EnhancedProcess.create_with_proxy(
        target=basic_worker,
        proxy_objects={'counter': counter},
        proxy_config=config
    )

    print(f"[主进程 {os.getpid()}] 启动高级配置进程...")
    process.start()
    success = process.wait_for_completion(timeout=15)

    if success:
        print(f"✅ 示例4完成，高级配置成功")
    else:
        print(f"❌ 示例4失败，高级配置超时")

    return success


def example_5_error_handling():
    """
    示例5：错误处理机制
    """
    print("\n" + "=" * 60)
    print("示例5：错误处理机制")
    print("=" * 60)

    # 创建服务对象
    counter = CounterService(initial_value=50)

    # 创建进程
    process = EnhancedProcess.create_with_proxy(
        target=error_handling_worker,
        proxy_objects={'counter': counter}
    )

    print(f"[主进程 {os.getpid()}] 启动错误处理进程...")
    process.start()
    success = process.wait_for_completion(timeout=10)

    if success:
        print(f"✅ 示例5完成，错误处理成功")
    else:
        print(f"❌ 示例5失败，错误处理超时")

    return success


def example_6_manager_info():
    """
    示例6：代理管理器信息获取
    """
    print("\n" + "=" * 60)
    print("示例6：代理管理器信息获取")
    print("=" * 60)

    # 创建服务对象
    counter = CounterService(initial_value=60)

    # 创建进程
    process = EnhancedProcess.create_with_proxy(
        target=basic_worker,
        proxy_objects={'counter': counter}
    )

    print(f"[主进程 {os.getpid()}] 启动进程...")
    process.start()

    # 获取代理管理器信息
    try:
        manager_info = process.get_proxy_manager_info()
        print(f"[主进程 {os.getpid()}] 代理管理器信息: {manager_info}")

        # 使用管理器信息创建新的代理连接
        from global_tools.utils.enhanced_process.proxy_accessor import ProxyAccessor
        accessor = ProxyAccessor(manager_info)

        # 通过新连接访问代理对象
        counter_proxy = accessor.get_proxy('counter')
        status = counter_proxy.get_status()
        print(f"[主进程 {os.getpid()}] 通过新连接获取状态: {status}")

    except Exception as e:
        print(f"[主进程 {os.getpid()}] 获取管理器信息失败: {e}")

    success = process.wait_for_completion(timeout=10)

    if success:
        print(f"✅ 示例6完成，管理器信息获取成功")
    else:
        print(f"❌ 示例6失败，管理器信息获取超时")

    return success


def main():
    """主函数 - 运行所有示例"""
    print("=" * 80)
    print("EnhancedProcess ProcessProxy 集成功能示例")
    print("=" * 80)

    # 运行所有示例
    results = []

    results.append(example_1_basic_usage())
    results.append(example_2_factory_function())
    results.append(example_3_decorator_pattern())
    results.append(example_4_advanced_configuration())
    results.append(example_5_error_handling())
    results.append(example_6_manager_info())

    # 汇总结果
    passed = sum(results)
    total = len(results)

    print("\n" + "=" * 80)
    print("示例运行结果汇总")
    print("=" * 80)
    print(f"总示例数: {total}")
    print(f"成功数: {passed}")
    print(f"失败数: {total - passed}")

    if passed == total:
        print("\n🎉 所有示例运行成功！")
        return True
    else:
        print(f"\n❌ {total - passed} 个示例运行失败！")
        return False


if __name__ == "__main__":
    # 设置多进程启动方法
    if hasattr(multiprocessing, 'set_start_method'):
        try:
            multiprocessing.set_start_method('spawn', force=True)
        except RuntimeError:
            pass  # 已经设置过了
    
    success = main()
    sys.exit(0 if success else 1)
