#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试跨进程调用的同步/异步行为
"""

import sys
import os
import time
import multiprocessing
import threading

# 添加项目路径
sys.path.insert(0, os.path.abspath('.'))

from global_tools.utils.enhanced_process.proxy_manager import ProcessProxyManager, ProcessProxy, ProxyConfiguration


class SlowTestObject:
    """测试对象，包含慢速方法来验证同步行为"""
    
    def __init__(self):
        self.counter = 0
        self.call_log = []
    
    def slow_increment(self, delay_seconds=2):
        """慢速递增方法"""
        start_time = time.time()
        print(f"[服务端] slow_increment 开始执行，延迟 {delay_seconds} 秒")
        
        # 记录调用开始
        self.call_log.append(f"slow_increment_start_{time.time()}")
        
        # 模拟耗时操作
        time.sleep(delay_seconds)
        
        # 执行实际操作
        self.counter += 1
        end_time = time.time()
        
        # 记录调用结束
        self.call_log.append(f"slow_increment_end_{time.time()}")
        
        print(f"[服务端] slow_increment 执行完成，耗时: {end_time - start_time:.2f}秒，计数器: {self.counter}")
        return self.counter
    
    def fast_get_counter(self):
        """快速获取计数器方法"""
        print(f"[服务端] fast_get_counter 被调用，当前计数器: {self.counter}")
        self.call_log.append(f"fast_get_counter_{time.time()}")
        return self.counter
    
    def get_call_log(self):
        """获取调用日志"""
        return self.call_log.copy()


def child_process_worker(manager_address, manager_authkey):
    """子进程工作函数"""
    try:
        print(f"\n[子进程 {os.getpid()}] 开始执行...")
        
        # 连接到主进程的代理管理器
        manager = ProcessProxyManager(
            address=manager_address,
            authkey=manager_authkey
        )
        manager.connect()
        print(f"[子进程 {os.getpid()}] 已连接到代理管理器")
        
        # 创建代理对象
        proxy = ProcessProxy(manager, 'slow_test_obj')
        print(f"[子进程 {os.getpid()}] 已创建ProcessProxy代理对象")
        
        # 测试同步行为
        print(f"\n[子进程 {os.getpid()}] === 同步行为测试 ===")
        
        # 调用1：慢速方法（2秒延迟）
        print(f"[子进程 {os.getpid()}] 调用 slow_increment(2) - 开始时间: {time.time()}")
        start_time = time.time()
        result1 = proxy.slow_increment(2)
        end_time = time.time()
        print(f"[子进程 {os.getpid()}] slow_increment(2) 返回: {result1}, 耗时: {end_time - start_time:.2f}秒")
        
        # 调用2：快速方法（立即执行）
        print(f"[子进程 {os.getpid()}] 调用 fast_get_counter() - 开始时间: {time.time()}")
        start_time = time.time()
        result2 = proxy.fast_get_counter()
        end_time = time.time()
        print(f"[子进程 {os.getpid()}] fast_get_counter() 返回: {result2}, 耗时: {end_time - start_time:.2f}秒")
        
        # 调用3：再次慢速方法（1秒延迟）
        print(f"[子进程 {os.getpid()}] 调用 slow_increment(1) - 开始时间: {time.time()}")
        start_time = time.time()
        result3 = proxy.slow_increment(1)
        end_time = time.time()
        print(f"[子进程 {os.getpid()}] slow_increment(1) 返回: {result3}, 耗时: {end_time - start_time:.2f}秒")
        
        # 获取调用日志
        call_log = proxy.get_call_log()
        print(f"[子进程 {os.getpid()}] 调用日志: {call_log}")
        
        print(f"[子进程 {os.getpid()}] 所有测试完成")
        return True
        
    except Exception as e:
        print(f"[子进程 {os.getpid()}] 发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=" * 80)
    print("跨进程调用同步/异步行为测试")
    print("=" * 80)
    
    try:
        # 创建测试对象
        print(f"[主进程 {os.getpid()}] 创建测试对象...")
        test_obj = SlowTestObject()
        
        # 创建并启动代理管理器
        print(f"[主进程 {os.getpid()}] 启动代理管理器...")
        config = ProxyConfiguration()
        config.debug_mode = True
        
        manager = ProcessProxyManager(config=config)
        manager.start()
        print(f"[主进程 {os.getpid()}] 代理管理器已启动，地址: {manager.address}")
        
        # 注册测试对象
        manager.register_object('slow_test_obj', test_obj)
        print(f"[主进程 {os.getpid()}] 测试对象已注册")
        
        # 启动子进程
        print(f"\n[主进程 {os.getpid()}] 启动子进程...")
        authkey = getattr(manager, '_authkey', b'process_proxy_default')
        process = multiprocessing.Process(
            target=child_process_worker,
            args=(manager.address, authkey)
        )
        
        # 记录开始时间
        test_start_time = time.time()
        process.start()
        
        # 等待子进程完成
        process.join(timeout=30)
        test_end_time = time.time()
        
        if process.is_alive():
            print(f"[主进程 {os.getpid()}] 子进程超时，强制终止")
            process.terminate()
            process.join()
            success = False
        else:
            success = process.exitcode == 0
            print(f"[主进程 {os.getpid()}] 子进程完成，退出码: {process.exitcode}")
        
        print(f"[主进程 {os.getpid()}] 总测试时间: {test_end_time - test_start_time:.2f}秒")
        
        # 检查最终状态
        final_counter = test_obj.counter
        final_log = test_obj.get_call_log()
        print(f"[主进程 {os.getpid()}] 最终计数器值: {final_counter}")
        print(f"[主进程 {os.getpid()}] 最终调用日志: {final_log}")
        
        # 关闭代理管理器
        manager.shutdown()
        
        return success
        
    except Exception as e:
        print(f"[主进程 {os.getpid()}] 发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # 设置多进程启动方法
    if hasattr(multiprocessing, 'set_start_method'):
        try:
            multiprocessing.set_start_method('spawn', force=True)
        except RuntimeError:
            pass
    
    success = main()
    print(f"\n测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
