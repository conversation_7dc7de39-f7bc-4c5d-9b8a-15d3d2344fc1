import sys
import os
import time
import multiprocessing
from typing import Dict, Any, List

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

# 导入 EnhancedProcess 相关模块
from global_tools.utils.enhanced_process import (
    EnhancedProcess,
    create_proxy_process,
    create_simple_proxy_worker,
    ProxyConfiguration,
    SharedDataManager,
    ProcessLogger,
    
)

class MinimalService:
    def __init__(self):
        self.value = 0
        
    def set_value(self, new_value):
        """最简单的setter方法"""
        print(f"[主进程] MinimalService.set_value({new_value}) 被调用，当前时间: {time.time():.6f}")
        self.value = new_value
        print(f"[主进程] MinimalService.set_value 执行完成，value = {self.value}")

service = MinimalService()

def minimal_debug_worker(shared_data_proxy: SharedDataManager, data_queue, process_logger: ProcessLogger, proxy_accessor):
    """最简单的调试工作函数"""
    try:
        s = proxy_accessor.service
        
        print(f"\n=== 最简单调试测试开始 ===")
        print(f"[子进程] 准备调用 s.set_value(42)")
        
        # 只调用一个简单的setter方法
        s.set_value(42)
        
        print(f"[子进程] s.set_value(42) 调用完成")
        print(f"=== 最简单调试测试结束 ===")
        
    except Exception as e:
        import traceback
        print(f"测试过程中发生错误: {e}")
        traceback.print_exc()

def main():
    print("开始最简单调试测试...")
    
    process = EnhancedProcess.create_with_proxy(
        target=minimal_debug_worker,
        proxy_objects={'service': service}
    )

    process.start()
    process.wait_for_completion()
    
    print("最简单调试测试完成！")

if __name__ == "__main__":
    main()
