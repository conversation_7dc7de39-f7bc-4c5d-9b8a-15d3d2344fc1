# -*- coding: utf-8 -*-
"""
ProxyAccessor - 进程代理访问器
=====================================

该模块为子进程提供简洁的代理对象访问接口，支持透明的跨进程对象访问。

核心功能：
1. 统一的代理对象访问接口
2. 智能缓存和连接管理
3. 多种访问方式支持
4. 自动重连和错误恢复

使用示例：
---------
# 在子进程中
def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor, *args, **kwargs):
    # 方法1：通过get_proxy获取代理对象
    service = proxy_accessor.get_proxy('service')
    result = service.increment()
    
    # 方法2：通过属性访问获取代理对象
    database = proxy_accessor.database
    data = database.query('SELECT * FROM users')
    
    # 方法3：列出可用对象
    available = proxy_accessor.list_available_objects()
    print(f"可用对象: {available}")

作者：Augment Agent
版本：1.0.0
"""

import time
import threading
from typing import Any, Dict, List, Optional
from global_tools.utils import Logger, Colors, ClassInstanceManager

# 获取日志记录器
logger: Logger = ClassInstanceManager.get_instance("ProxyAccessorLogger")


class ProxyAccessor:
    """
    代理访问器 - 为子进程提供简洁的代理对象访问接口
    
    该类为子进程提供统一的代理对象访问接口，支持多种访问方式：
    1. 方法调用：proxy_accessor.get_proxy('service')
    2. 属性访问：proxy_accessor.service
    3. 对象发现：proxy_accessor.list_available_objects()
    
    功能特性：
    - 智能缓存：自动缓存代理对象，提高访问效率
    - 连接管理：自动管理与主进程的连接
    - 错误恢复：支持连接中断后的自动重连
    - 统计信息：提供详细的使用统计
    
    使用示例：
        # 在子进程的worker函数中
        def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            service = proxy_accessor.get_proxy('service')
            result = service.increment()
            
            # 或者使用属性访问方式
            database = proxy_accessor.database
            data = database.query('SELECT * FROM users')
    """
    
    def __init__(self, proxy_manager_info: Dict[str, Any]):
        """
        初始化代理访问器
        
        Args:
            proxy_manager_info (Dict[str, Any]): 代理管理器连接信息，包含：
                - address: 管理器地址 (host, port)
                - authkey: 认证密钥
                - registered_objects: 已注册的对象ID列表
        
        使用示例：
            proxy_info = {
                'address': ('127.0.0.1', 50000),
                'authkey': b'secret_key',
                'registered_objects': ['service', 'database']
            }
            accessor = ProxyAccessor(proxy_info)
        """
        self.__proxy_manager_info = proxy_manager_info
        self.__proxy_manager = None  # 延迟初始化
        self.__proxy_cache = {}  # 代理对象缓存 {obj_id: proxy}
        self.__access_count = {}  # 访问计数 {obj_id: count}
        self.__last_access = {}  # 最后访问时间 {obj_id: timestamp}
        self.__lock = threading.Lock()  # 线程安全锁
        self.__connected = False  # 连接状态
        
        logger.debug(f"ProxyAccessor已初始化，管理器地址: {proxy_manager_info.get('address')}", 
                    color=Colors.DEBUG)
    
    def get_proxy(self, obj_id: str):
        """
        获取代理对象
        
        Args:
            obj_id (str): 对象ID
        
        Returns:
            ProcessProxy: 代理对象实例
        
        Raises:
            KeyError: 如果对象ID不存在
            ConnectionError: 如果无法连接到代理管理器
        
        使用示例：
            service = proxy_accessor.get_proxy('service')
            result = service.increment()
        """
        with self.__lock:
            # 检查缓存
            if obj_id in self.__proxy_cache:
                self.__update_access_stats(obj_id)
                logger.debug(f"从缓存返回代理对象: {obj_id}", color=Colors.DEBUG)
                return self.__proxy_cache[obj_id]
            
            # 确保连接已建立
            self.__ensure_connected()
            
            # 检查对象是否已注册
            registered_objects = self.__proxy_manager_info.get('registered_objects', [])
            if obj_id not in registered_objects:
                raise KeyError(f"对象ID '{obj_id}' 未注册，可用对象: {registered_objects}")
            
            # 创建代理对象
            try:
                proxy = self.__proxy_manager.create_proxy(obj_id)
                self.__proxy_cache[obj_id] = proxy
                self.__update_access_stats(obj_id)
                
                logger.info(f"代理对象已创建并缓存: {obj_id}", color=Colors.INFO)
                return proxy
                
            except Exception as e:
                logger.error(f"创建代理对象失败: {obj_id}, 错误: {e}", color=Colors.ERROR)
                raise
    
    def __getattr__(self, name: str):
        """
        属性访问支持，允许通过 proxy_accessor.service 的方式访问代理对象
        
        Args:
            name (str): 属性名称（对象ID）
        
        Returns:
            ProcessProxy: 代理对象实例
        
        使用示例：
            service = proxy_accessor.service  # 等同于 get_proxy('service')
            database = proxy_accessor.database  # 等同于 get_proxy('database')
        """
        # 避免递归调用
        if name.startswith('_ProxyAccessor__'):
            return object.__getattribute__(self, name)
        
        try:
            return self.get_proxy(name)
        except KeyError:
            raise AttributeError(f"'{self.__class__.__name__}' 对象没有属性 '{name}'，可用对象: {self.list_available_objects()}")
    
    def list_available_objects(self) -> List[str]:
        """
        列出可用的代理对象
        
        Returns:
            List[str]: 可用的对象ID列表
        
        使用示例：
            objects = proxy_accessor.list_available_objects()
            print(f"可用对象: {objects}")
        """
        return self.__proxy_manager_info.get('registered_objects', [])
    
    def refresh_connection(self):
        """
        刷新连接，重新连接到代理管理器
        
        该方法会清除所有缓存的代理对象并重新建立连接。
        
        使用示例：
            try:
                result = proxy_accessor.service.some_method()
            except ConnectionError:
                proxy_accessor.refresh_connection()
                result = proxy_accessor.service.some_method()
        """
        with self.__lock:
            # 清除缓存
            self.__proxy_cache.clear()
            self.__connected = False
            
            # 关闭现有连接
            if self.__proxy_manager:
                try:
                    self.__proxy_manager.shutdown()
                except:
                    pass
                self.__proxy_manager = None
            
            logger.info("代理连接已刷新", color=Colors.INFO)
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取访问统计信息
        
        Returns:
            Dict[str, Any]: 统计信息，包含：
                - cached_objects: 缓存的对象数量
                - access_count: 各对象的访问次数
                - last_access: 各对象的最后访问时间
                - connection_status: 连接状态
        
        使用示例：
            stats = proxy_accessor.get_statistics()
            print(f"统计信息: {stats}")
        """
        with self.__lock:
            return {
                'cached_objects': len(self.__proxy_cache),
                'access_count': self.__access_count.copy(),
                'last_access': self.__last_access.copy(),
                'connection_status': self.__connected,
                'available_objects': self.list_available_objects()
            }
    
    def __ensure_connected(self):
        """
        确保与代理管理器的连接已建立（私有方法）
        """
        if not self.__connected:
            try:
                # 导入ProcessProxyManager
                from .proxy_manager import ProcessProxyManager
                
                # 创建代理管理器连接
                address = self.__proxy_manager_info['address']
                authkey = self.__proxy_manager_info['authkey']
                
                self.__proxy_manager = ProcessProxyManager(address=address, authkey=authkey)
                self.__proxy_manager.connect()
                
                self.__connected = True
                logger.info(f"已连接到代理管理器: {address}", color=Colors.SUCCESS)
                
            except Exception as e:
                logger.error(f"连接代理管理器失败: {e}", color=Colors.ERROR)
                raise ConnectionError(f"无法连接到代理管理器: {e}")
    
    def __update_access_stats(self, obj_id: str):
        """
        更新访问统计信息（私有方法）
        
        Args:
            obj_id (str): 对象ID
        """
        self.__access_count[obj_id] = self.__access_count.get(obj_id, 0) + 1
        self.__last_access[obj_id] = time.time()
    
    def __del__(self):
        """析构函数，清理资源"""
        try:
            if self.__proxy_manager:
                self.__proxy_manager.shutdown()
        except:
            pass


# 模块导出
__all__ = [
    'ProxyAccessor',
]
