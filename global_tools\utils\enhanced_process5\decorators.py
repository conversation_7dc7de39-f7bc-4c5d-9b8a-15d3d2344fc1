# -*- coding: utf-8 -*-
"""
ProcessProxy装饰器模块
====================

该模块提供便捷的装饰器，简化ProcessProxy功能的使用。

核心功能：
1. 对象注册装饰器
2. 函数签名标记装饰器
3. 上下文管理器
4. 配置装饰器

使用示例：
---------
# 装饰器方式注册对象
@with_proxy_objects({'service': service_instance})
def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    service = proxy_accessor.service
    return service.increment()

# 标记函数支持ProcessProxy
@process_proxy_enabled
def worker_with_proxy(shared_data_proxy, data_queue, process_logger, proxy_accessor):
    pass

作者：Augment Agent
版本：1.0.0
"""

import functools
from typing import Any, Dict, Callable, Optional
from .proxy_config import ProcessProxyConfig


def with_proxy_objects(objects: Dict[str, Any], config: Optional[ProcessProxyConfig] = None):
    """
    为函数添加代理对象的装饰器
    
    该装饰器会自动为被装饰的函数配置ProcessProxy对象，
    使其能够在EnhancedProcess中使用代理功能。
    
    Args:
        objects (Dict[str, Any]): 要注册的代理对象字典 {obj_id: obj_instance}
        config (ProcessProxyConfig, optional): ProcessProxy配置，如果为None则使用默认配置
    
    Returns:
        Callable: 装饰后的函数
    
    使用示例：
        service = MyService()
        database = DatabaseService()
        
        @with_proxy_objects({
            'service': service,
            'database': database
        })
        def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            # 使用代理对象
            result = proxy_accessor.service.increment()
            data = proxy_accessor.database.query('SELECT * FROM users')
            return result
        
        # 创建进程时会自动启用代理功能
        process = EnhancedProcess(target=worker)
        process.start()
    """
    def decorator(func: Callable) -> Callable:
        # 为函数添加代理配置属性
        func._proxy_objects = objects.copy()
        func._proxy_config = config
        func._proxy_enabled = True
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        
        # 保持代理配置属性
        wrapper._proxy_objects = objects.copy()
        wrapper._proxy_config = config
        wrapper._proxy_enabled = True
        
        return wrapper
    
    return decorator


def process_proxy_enabled(func: Callable) -> Callable:
    """
    标记函数支持ProcessProxy的装饰器
    
    该装饰器用于明确标记一个函数支持ProcessProxy功能，
    主要用于函数签名检测和文档目的。
    
    Args:
        func (Callable): 要标记的函数
    
    Returns:
        Callable: 标记后的函数
    
    使用示例：
        @process_proxy_enabled
        def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            # 这个函数支持ProcessProxy功能
            service = proxy_accessor.get_proxy('service')
            return service.increment()
        
        # EnhancedProcess会自动检测到这个标记
        process = EnhancedProcess(target=worker, enable_proxy=True)
    """
    func._proxy_enabled = True
    func._supports_proxy_accessor = True
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)
    
    # 保持标记属性
    wrapper._proxy_enabled = True
    wrapper._supports_proxy_accessor = True
    
    return wrapper


def with_proxy_config(config: ProcessProxyConfig):
    """
    为函数添加ProcessProxy配置的装饰器
    
    Args:
        config (ProcessProxyConfig): ProcessProxy配置对象
    
    Returns:
        Callable: 装饰器函数
    
    使用示例：
        config = ProcessProxyConfig()
        config.debug_mode = True
        config.register_object('service', service)
        
        @with_proxy_config(config)
        def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            return proxy_accessor.service.increment()
    """
    def decorator(func: Callable) -> Callable:
        func._proxy_config = config
        func._proxy_objects = config.get_registered_objects()
        func._proxy_enabled = config.enabled
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        
        # 保持配置属性
        wrapper._proxy_config = config
        wrapper._proxy_objects = config.get_registered_objects()
        wrapper._proxy_enabled = config.enabled
        
        return wrapper
    
    return decorator


class ProcessProxyContext:
    """
    ProcessProxy上下文管理器
    
    该类提供上下文管理器接口，用于临时配置ProcessProxy环境。
    
    使用示例：
        with ProcessProxyContext() as proxy_ctx:
            proxy_ctx.register('service', service_instance)
            proxy_ctx.register('database', database_instance)
            
            process = EnhancedProcess(
                target=worker,
                enable_proxy=True,
                proxy_objects=proxy_ctx.get_objects()
            )
            process.start()
    """
    
    def __init__(self, config: Optional[ProcessProxyConfig] = None):
        """
        初始化ProcessProxy上下文管理器
        
        Args:
            config (ProcessProxyConfig, optional): ProcessProxy配置，如果为None则创建默认配置
        
        使用示例：
            # 使用默认配置
            with ProcessProxyContext() as ctx:
                ctx.register('service', service)
            
            # 使用自定义配置
            config = ProcessProxyConfig()
            config.debug_mode = True
            with ProcessProxyContext(config) as ctx:
                ctx.register('service', service)
        """
        self._config = config if config is not None else ProcessProxyConfig()
        self._objects = {}
    
    def __enter__(self):
        """进入上下文"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文"""
        # 清理资源
        self._objects.clear()
        return False  # 不抑制异常
    
    def register(self, obj_id: str, obj_instance: Any):
        """
        注册代理对象
        
        Args:
            obj_id (str): 对象ID
            obj_instance (Any): 对象实例
        
        使用示例：
            with ProcessProxyContext() as ctx:
                ctx.register('service', service_instance)
                ctx.register('database', database_instance)
        """
        self._objects[obj_id] = obj_instance
        self._config.register_object(obj_id, obj_instance)
    
    def unregister(self, obj_id: str):
        """
        注销代理对象
        
        Args:
            obj_id (str): 对象ID
        
        使用示例：
            with ProcessProxyContext() as ctx:
                ctx.register('service', service)
                ctx.unregister('service')
        """
        if obj_id in self._objects:
            del self._objects[obj_id]
        self._config.unregister_object(obj_id)
    
    def get_objects(self) -> Dict[str, Any]:
        """
        获取已注册的对象字典
        
        Returns:
            Dict[str, Any]: 对象字典
        
        使用示例：
            with ProcessProxyContext() as ctx:
                ctx.register('service', service)
                objects = ctx.get_objects()
                process = EnhancedProcess(target=worker, proxy_objects=objects)
        """
        return self._objects.copy()
    
    def get_config(self) -> ProcessProxyConfig:
        """
        获取ProcessProxy配置
        
        Returns:
            ProcessProxyConfig: 配置对象
        
        使用示例：
            with ProcessProxyContext() as ctx:
                ctx.register('service', service)
                config = ctx.get_config()
                process = EnhancedProcess(
                    target=worker,
                    enable_proxy=config.enabled,
                    proxy_config=config.get_proxy_configuration(),
                    proxy_objects=config.get_registered_objects()
                )
        """
        return self._config


def extract_proxy_config(func: Callable) -> tuple:
    """
    从装饰的函数中提取ProcessProxy配置
    
    该函数用于从被装饰的函数中提取代理配置信息，
    供EnhancedProcess自动配置使用。
    
    Args:
        func (Callable): 被装饰的函数
    
    Returns:
        tuple: (enabled, config, objects) 元组
            - enabled (bool): 是否启用代理功能
            - config (ProcessProxyConfig): 代理配置对象
            - objects (Dict[str, Any]): 代理对象字典
    
    使用示例：
        @with_proxy_objects({'service': service})
        def worker(shared_data_proxy, data_queue, process_logger, proxy_accessor):
            pass
        
        enabled, config, objects = extract_proxy_config(worker)
        process = EnhancedProcess(
            target=worker,
            enable_proxy=enabled,
            proxy_config=config,
            proxy_objects=objects
        )
    """
    enabled = getattr(func, '_proxy_enabled', False)
    config = getattr(func, '_proxy_config', None)
    objects = getattr(func, '_proxy_objects', {})
    
    return enabled, config, objects


# 模块导出
__all__ = [
    'with_proxy_objects',
    'process_proxy_enabled',
    'with_proxy_config',
    'ProcessProxyContext',
    'extract_proxy_config'
]
