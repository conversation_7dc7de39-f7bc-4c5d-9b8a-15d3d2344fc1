# -*- coding: utf-8 -*-
"""
ProcessProxy配置模块
==================

该模块提供ProcessProxy集成的配置管理功能。

核心功能：
1. 统一的配置管理
2. 配置验证和默认值
3. 配置序列化和反序列化
4. 配置继承和覆盖

使用示例：
---------
# 基础使用
config = ProcessProxyConfig()
config.enabled = True
config.register_object('service', service_instance)

# 高级配置
config = ProcessProxyConfig()
config.auto_detect_signature = True
config.fallback_mode = 'strict'
config.lazy_initialization = False

作者：Augment Agent
版本：1.0.0
"""

import copy
from typing import Any, Dict, Optional
from .proxy_manager import ProxyConfiguration


class ProcessProxyConfig:
    """
    ProcessProxy集成配置类
    
    该类管理EnhancedProcess与ProcessProxy集成的所有配置选项，
    提供统一的配置接口和验证机制。
    
    功能特性：
    - 统一配置管理：集中管理所有ProcessProxy相关配置
    - 自动验证：确保配置的有效性和一致性
    - 默认值管理：提供合理的默认配置
    - 配置继承：支持配置的继承和覆盖
    
    使用示例：
        # 基础配置
        config = ProcessProxyConfig()
        config.enabled = True
        config.register_object('service', service_instance)
        
        # 创建EnhancedProcess
        process = EnhancedProcess(
            target=worker,
            enable_proxy=config.enabled,
            proxy_config=config.get_proxy_configuration(),
            proxy_objects=config.get_registered_objects()
        )
    """
    
    def __init__(self):
        """
        初始化ProcessProxy配置
        
        使用示例：
            config = ProcessProxyConfig()
            print(f"默认配置: enabled={config.enabled}")
        """
        # 基础配置
        self.enabled: bool = False
        self.auto_detect_signature: bool = True
        self.lazy_initialization: bool = True
        self.fallback_mode: str = 'compatible'  # 'compatible' | 'strict'
        
        # 对象注册
        self._registered_objects: Dict[str, Any] = {}
        
        # ProcessProxy配置
        self._proxy_config: Optional[ProxyConfiguration] = None
        
        # 高级配置
        self.debug_mode: bool = False
        self.performance_monitoring: bool = False
        self.cache_enabled: bool = True
        self.cache_ttl: float = 300.0  # 5分钟
        self.connection_timeout: float = 10.0
        self.retry_attempts: int = 3
        self.retry_delay: float = 1.0
    
    def register_object(self, obj_id: str, obj_instance: Any):
        """
        注册要代理的对象
        
        Args:
            obj_id (str): 对象ID，用于在子进程中引用
            obj_instance (Any): 要注册的对象实例
        
        Raises:
            ValueError: 如果obj_id已存在或参数无效
        
        使用示例：
            config = ProcessProxyConfig()
            service = MyService()
            config.register_object('my_service', service)
        """
        if not obj_id:
            raise ValueError("obj_id 不能为空")
        if obj_instance is None:
            raise ValueError("obj_instance 不能为None")
        if obj_id in self._registered_objects:
            raise ValueError(f"对象ID '{obj_id}' 已存在")
        
        self._registered_objects[obj_id] = obj_instance
        self.enabled = True  # 注册对象时自动启用
    
    def unregister_object(self, obj_id: str):
        """
        注销代理对象
        
        Args:
            obj_id (str): 要注销的对象ID
        
        Raises:
            KeyError: 如果obj_id不存在
        
        使用示例：
            config.unregister_object('my_service')
        """
        if obj_id not in self._registered_objects:
            raise KeyError(f"对象ID '{obj_id}' 不存在")
        
        del self._registered_objects[obj_id]
        
        # 如果没有注册对象了，可以选择禁用
        if not self._registered_objects:
            self.enabled = False
    
    def get_registered_objects(self) -> Dict[str, Any]:
        """
        获取已注册的对象字典
        
        Returns:
            Dict[str, Any]: 已注册对象的副本
        
        使用示例：
            objects = config.get_registered_objects()
            print(f"已注册对象: {list(objects.keys())}")
        """
        return self._registered_objects.copy()
    
    def list_registered_object_ids(self) -> list:
        """
        获取已注册的对象ID列表
        
        Returns:
            list: 对象ID列表
        
        使用示例：
            ids = config.list_registered_object_ids()
            print(f"对象IDs: {ids}")
        """
        return list(self._registered_objects.keys())
    
    def has_object(self, obj_id: str) -> bool:
        """
        检查对象是否已注册
        
        Args:
            obj_id (str): 对象ID
        
        Returns:
            bool: 是否已注册
        
        使用示例：
            if config.has_object('my_service'):
                print("服务已注册")
        """
        return obj_id in self._registered_objects
    
    def set_proxy_configuration(self, proxy_config: ProxyConfiguration):
        """
        设置ProcessProxy配置
        
        Args:
            proxy_config (ProxyConfiguration): ProcessProxy配置对象
        
        使用示例：
            proxy_config = ProxyConfiguration()
            proxy_config.debug_mode = True
            config.set_proxy_configuration(proxy_config)
        """
        self._proxy_config = proxy_config
    
    def get_proxy_configuration(self) -> ProxyConfiguration:
        """
        获取ProcessProxy配置
        
        Returns:
            ProxyConfiguration: ProcessProxy配置对象
        
        使用示例：
            proxy_config = config.get_proxy_configuration()
            print(f"调试模式: {proxy_config.debug_mode}")
        """
        if self._proxy_config is None:
            # 创建默认配置
            self._proxy_config = ProxyConfiguration()
            self._proxy_config.debug_mode = self.debug_mode
            self._proxy_config.performance_monitoring = self.performance_monitoring
            self._proxy_config.cache_ttl = self.cache_ttl
            self._proxy_config.queue_timeout = self.connection_timeout
        
        return self._proxy_config
    
    def validate_config(self) -> bool:
        """
        验证配置的有效性
        
        Returns:
            bool: 配置是否有效
        
        Raises:
            ValueError: 如果配置无效
        
        使用示例：
            if config.validate_config():
                print("配置有效")
        """
        # 验证fallback_mode
        valid_fallback_modes = ['compatible', 'strict']
        if self.fallback_mode not in valid_fallback_modes:
            raise ValueError(f"无效的fallback_mode: {self.fallback_mode}, 有效值: {valid_fallback_modes}")
        
        # 验证数值范围
        if self.cache_ttl < 0:
            raise ValueError("cache_ttl 必须为非负数")
        
        if self.connection_timeout <= 0:
            raise ValueError("connection_timeout 必须为正数")
        
        if self.retry_attempts < 0:
            raise ValueError("retry_attempts 必须为非负数")
        
        if self.retry_delay < 0:
            raise ValueError("retry_delay 必须为非负数")
        
        # 验证对象注册
        if self.enabled and not self._registered_objects:
            raise ValueError("启用ProcessProxy功能时必须至少注册一个对象")
        
        return True
    
    def copy(self) -> 'ProcessProxyConfig':
        """
        创建配置的深拷贝
        
        Returns:
            ProcessProxyConfig: 配置的副本
        
        使用示例：
            config_copy = config.copy()
            config_copy.debug_mode = True  # 不影响原配置
        """
        new_config = ProcessProxyConfig()
        
        # 复制基础配置
        new_config.enabled = self.enabled
        new_config.auto_detect_signature = self.auto_detect_signature
        new_config.lazy_initialization = self.lazy_initialization
        new_config.fallback_mode = self.fallback_mode
        
        # 复制对象注册（深拷贝）
        new_config._registered_objects = copy.deepcopy(self._registered_objects)
        
        # 复制ProcessProxy配置
        if self._proxy_config is not None:
            new_config._proxy_config = copy.deepcopy(self._proxy_config)
        
        # 复制高级配置
        new_config.debug_mode = self.debug_mode
        new_config.performance_monitoring = self.performance_monitoring
        new_config.cache_enabled = self.cache_enabled
        new_config.cache_ttl = self.cache_ttl
        new_config.connection_timeout = self.connection_timeout
        new_config.retry_attempts = self.retry_attempts
        new_config.retry_delay = self.retry_delay
        
        return new_config
    
    def to_dict(self) -> Dict[str, Any]:
        """
        将配置转换为字典
        
        Returns:
            Dict[str, Any]: 配置字典
        
        使用示例：
            config_dict = config.to_dict()
            print(f"配置: {config_dict}")
        """
        return {
            'enabled': self.enabled,
            'auto_detect_signature': self.auto_detect_signature,
            'lazy_initialization': self.lazy_initialization,
            'fallback_mode': self.fallback_mode,
            'registered_object_count': len(self._registered_objects),
            'registered_object_ids': list(self._registered_objects.keys()),
            'debug_mode': self.debug_mode,
            'performance_monitoring': self.performance_monitoring,
            'cache_enabled': self.cache_enabled,
            'cache_ttl': self.cache_ttl,
            'connection_timeout': self.connection_timeout,
            'retry_attempts': self.retry_attempts,
            'retry_delay': self.retry_delay
        }
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ProcessProxyConfig(enabled={self.enabled}, objects={len(self._registered_objects)})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"ProcessProxyConfig(enabled={self.enabled}, "
                f"auto_detect_signature={self.auto_detect_signature}, "
                f"fallback_mode='{self.fallback_mode}', "
                f"registered_objects={len(self._registered_objects)})")


# 便捷函数
def create_default_config() -> ProcessProxyConfig:
    """
    创建默认配置
    
    Returns:
        ProcessProxyConfig: 默认配置实例
    
    使用示例：
        config = create_default_config()
        config.register_object('service', service)
    """
    return ProcessProxyConfig()


def create_debug_config() -> ProcessProxyConfig:
    """
    创建调试配置
    
    Returns:
        ProcessProxyConfig: 调试配置实例
    
    使用示例：
        config = create_debug_config()
        config.register_object('service', service)
    """
    config = ProcessProxyConfig()
    config.debug_mode = True
    config.performance_monitoring = True
    config.fallback_mode = 'strict'
    return config


def create_performance_config() -> ProcessProxyConfig:
    """
    创建性能优化配置
    
    Returns:
        ProcessProxyConfig: 性能优化配置实例
    
    使用示例：
        config = create_performance_config()
        config.register_object('service', service)
    """
    config = ProcessProxyConfig()
    config.lazy_initialization = True
    config.cache_enabled = True
    config.cache_ttl = 600.0  # 10分钟
    config.performance_monitoring = True
    return config


# 模块导出
__all__ = [
    'ProcessProxyConfig',
    'create_default_config',
    'create_debug_config',
    'create_performance_config'
]
